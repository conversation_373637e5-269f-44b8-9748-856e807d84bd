package com.ruoyi.crm.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ruoyi.common.domain.entity.CrmTeamMember;
import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.common.mapper.CrmTeamMemberMapper;
import com.ruoyi.common.mapper.CrmTeamRelationMapper;
import com.ruoyi.crm.service.impl.CrmTeamMemberServiceImpl;

/**
 * CrmTeamMemberService 单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("团队成员服务单元测试")
class CrmTeamMemberServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(CrmTeamMemberServiceTest.class);

    @Mock
    private CrmTeamMemberMapper crmTeamMemberMapper;

    @Mock
    private CrmTeamRelationMapper crmTeamRelationMapper;

    @InjectMocks
    private CrmTeamMemberServiceImpl crmTeamMemberService;

    private CrmTeamMember testMember;
    private Long testTeamId = 1L;
    private Long testUserId = 100L;
    private Long testMemberId = 1L;

    @BeforeEach
    void setUp() {
        logger.info("=== 开始设置测试数据 ===");
        
        testMember = new CrmTeamMember();
        testMember.setId(testMemberId);
        testMember.setTeamId(testTeamId);
        testMember.setUserId(testUserId);
        testMember.setUserName("testUser");
        testMember.setNickName("测试用户");
        testMember.setRoleType("member");
        testMember.setStatus("0");
        testMember.setJoinTime(new Date());
    }

    @Nested
    @DisplayName("基础CRUD操作测试")
    class BasicCrudTests {

        @Test
        @DisplayName("根据ID查询团队成员")
        void testSelectCrmTeamMemberById() {
            // Given
            when(crmTeamMemberMapper.selectCrmTeamMemberById(testMemberId)).thenReturn(testMember);

            // When
            CrmTeamMember result = crmTeamMemberService.selectCrmTeamMemberById(testMemberId);

            // Then
            assertNotNull(result);
            assertEquals(testMemberId, result.getId());
            assertEquals(testTeamId, result.getTeamId());
            assertEquals(testUserId, result.getUserId());
            verify(crmTeamMemberMapper).selectCrmTeamMemberById(testMemberId);
        }

        @Test
        @DisplayName("查询团队成员列表")
        void testSelectCrmTeamMemberList() {
            // Given
            List<CrmTeamMember> expectedList = Arrays.asList(testMember);
            CrmTeamMember query = new CrmTeamMember();
            query.setTeamId(testTeamId);
            
            when(crmTeamMemberMapper.selectCrmTeamMemberList(any(CrmTeamMember.class))).thenReturn(expectedList);

            // When
            List<CrmTeamMember> result = crmTeamMemberService.selectCrmTeamMemberList(query);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testMember.getId(), result.get(0).getId());
            verify(crmTeamMemberMapper).selectCrmTeamMemberList(query);
        }

        @Test
        @DisplayName("新增团队成员")
        void testInsertCrmTeamMember() {
            // Given
            CrmTeamMember newMember = new CrmTeamMember();
            newMember.setTeamId(testTeamId);
            newMember.setUserId(testUserId);
            
            when(crmTeamMemberMapper.insertCrmTeamMember(any(CrmTeamMember.class))).thenReturn(1);

            // When
            int result = crmTeamMemberService.insertCrmTeamMember(newMember);

            // Then
            assertEquals(1, result);
            assertNotNull(newMember.getJoinTime());
            assertEquals("0", newMember.getStatus());
            assertEquals("member", newMember.getRoleType());
            verify(crmTeamMemberMapper).insertCrmTeamMember(newMember);
        }

        @Test
        @DisplayName("更新团队成员")
        void testUpdateCrmTeamMember() {
            // Given
            when(crmTeamMemberMapper.updateCrmTeamMember(any(CrmTeamMember.class))).thenReturn(1);

            // When
            int result = crmTeamMemberService.updateCrmTeamMember(testMember);

            // Then
            assertEquals(1, result);
            verify(crmTeamMemberMapper).updateCrmTeamMember(testMember);
        }

        @Test
        @DisplayName("删除团队成员")
        void testDeleteCrmTeamMemberById() {
            // Given
            when(crmTeamMemberMapper.deleteCrmTeamMemberById(testMemberId)).thenReturn(1);

            // When
            int result = crmTeamMemberService.deleteCrmTeamMemberById(testMemberId);

            // Then
            assertEquals(1, result);
            verify(crmTeamMemberMapper).deleteCrmTeamMemberById(testMemberId);
        }

        @Test
        @DisplayName("批量删除团队成员")
        void testDeleteCrmTeamMemberByIds() {
            // Given
            Long[] ids = {1L, 2L, 3L};
            when(crmTeamMemberMapper.deleteCrmTeamMemberByIds(ids)).thenReturn(3);

            // When
            int result = crmTeamMemberService.deleteCrmTeamMemberByIds(ids);

            // Then
            assertEquals(3, result);
            verify(crmTeamMemberMapper).deleteCrmTeamMemberByIds(ids);
        }
    }

    @Nested
    @DisplayName("业务方法测试")
    class BusinessMethodTests {

        @Test
        @DisplayName("根据团队ID查询成员列表")
        void testGetTeamMembersByTeamId() {
            // Given
            List<CrmTeamMember> expectedList = Arrays.asList(testMember);
            when(crmTeamMemberMapper.selectCrmTeamMembersByTeamId(testTeamId)).thenReturn(expectedList);

            // When
            List<CrmTeamMember> result = crmTeamMemberService.getTeamMembersByTeamId(testTeamId);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testMember.getId(), result.get(0).getId());
            verify(crmTeamMemberMapper).selectCrmTeamMembersByTeamId(testTeamId);
        }

        @Test
        @DisplayName("根据用户ID查询所属团队列表")
        void testGetTeamsByUserId() {
            // Given
            List<CrmTeamMember> expectedList = Arrays.asList(testMember);
            when(crmTeamMemberMapper.selectCrmTeamMemberList(any(CrmTeamMember.class))).thenReturn(expectedList);

            // When
            List<CrmTeamMember> result = crmTeamMemberService.getTeamsByUserId(testUserId);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(crmTeamMemberMapper).selectCrmTeamMemberList(any(CrmTeamMember.class));
        }

        @Test
        @DisplayName("添加团队成员 - 成功")
        void testAddTeamMember_Success() {
            // Given
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId)).thenReturn(null);
            when(crmTeamMemberMapper.insertCrmTeamMember(any(CrmTeamMember.class))).thenReturn(1);

            // When
            int result = crmTeamMemberService.addTeamMember(testTeamId, testUserId, "leader");

            // Then
            assertEquals(1, result);
            verify(crmTeamMemberMapper).selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId);
            verify(crmTeamMemberMapper).insertCrmTeamMember(any(CrmTeamMember.class));
        }

        @Test
        @DisplayName("添加团队成员 - 已存在应抛出异常")
        void testAddTeamMember_AlreadyExists() {
            // Given
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId)).thenReturn(testMember);

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                crmTeamMemberService.addTeamMember(testTeamId, testUserId, "member");
            });
            
            assertEquals("用户已经是团队成员", exception.getMessage());
            verify(crmTeamMemberMapper).selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId);
            verify(crmTeamMemberMapper, never()).insertCrmTeamMember(any(CrmTeamMember.class));
        }

        @Test
        @DisplayName("批量添加团队成员")
        void testBatchAddTeamMembers() {
            // Given
            List<Long> userIds = Arrays.asList(100L, 101L, 102L);
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(eq(testTeamId), anyLong())).thenReturn(null);
            when(crmTeamMemberMapper.insertCrmTeamMember(any(CrmTeamMember.class))).thenReturn(1);

            // When
            int result = crmTeamMemberService.batchAddTeamMembers(testTeamId, userIds, "member");

            // Then
            assertEquals(3, result);
            verify(crmTeamMemberMapper, times(3)).selectCrmTeamMemberByTeamIdAndUserId(eq(testTeamId), anyLong());
            verify(crmTeamMemberMapper, times(3)).insertCrmTeamMember(any(CrmTeamMember.class));
        }

        @Test
        @DisplayName("更新成员角色")
        void testUpdateMemberRole() {
            // Given
            when(crmTeamMemberMapper.updateCrmTeamMember(any(CrmTeamMember.class))).thenReturn(1);

            // When
            int result = crmTeamMemberService.updateMemberRole(testMemberId, "leader");

            // Then
            assertEquals(1, result);
            verify(crmTeamMemberMapper).updateCrmTeamMember(any(CrmTeamMember.class));
        }

        @Test
        @DisplayName("移除团队成员")
        void testRemoveTeamMember() {
            // Given
            when(crmTeamMemberMapper.deleteCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId)).thenReturn(1);

            // When
            int result = crmTeamMemberService.removeTeamMember(testTeamId, testUserId);

            // Then
            assertEquals(1, result);
            verify(crmTeamMemberMapper).deleteCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId);
        }

        @Test
        @DisplayName("检查用户是否为团队成员 - 是成员")
        void testIsTeamMember_True() {
            // Given
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId)).thenReturn(testMember);

            // When
            boolean result = crmTeamMemberService.isTeamMember(testTeamId, testUserId);

            // Then
            assertTrue(result);
            verify(crmTeamMemberMapper).selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId);
        }

        @Test
        @DisplayName("检查用户是否为团队成员 - 不是成员")
        void testIsTeamMember_False() {
            // Given
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId)).thenReturn(null);

            // When
            boolean result = crmTeamMemberService.isTeamMember(testTeamId, testUserId);

            // Then
            assertFalse(result);
            verify(crmTeamMemberMapper).selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId);
        }

        @Test
        @DisplayName("获取用户在团队中的角色")
        void testGetUserRoleInTeam() {
            // Given
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId)).thenReturn(testMember);

            // When
            String result = crmTeamMemberService.getUserRoleInTeam(testTeamId, testUserId);

            // Then
            assertEquals("member", result);
            verify(crmTeamMemberMapper).selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId);
        }

        @Test
        @DisplayName("根据业务对象获取团队成员")
        void testGetTeamMembersByBiz() {
            // Given
            Long bizId = 1L;
            String bizType = "CONTACT";
            CrmTeamRelation relation = new CrmTeamRelation();
            relation.setTeamId(testTeamId);
            
            List<CrmTeamMember> expectedMembers = Arrays.asList(testMember);
            
            when(crmTeamRelationMapper.selectTeamRelationByBiz(bizId, bizType)).thenReturn(relation);
            when(crmTeamMemberMapper.selectCrmTeamMembersByTeamId(testTeamId)).thenReturn(expectedMembers);

            // When
            List<CrmTeamMember> result = crmTeamMemberService.getTeamMembersByBiz(bizId, bizType);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testMember.getId(), result.get(0).getId());
            verify(crmTeamRelationMapper).selectTeamRelationByBiz(bizId, bizType);
            verify(crmTeamMemberMapper).selectCrmTeamMembersByTeamId(testTeamId);
        }

        @Test
        @DisplayName("根据业务对象获取团队成员 - 无关联团队")
        void testGetTeamMembersByBiz_NoRelation() {
            // Given
            Long bizId = 1L;
            String bizType = "CONTACT";
            
            when(crmTeamRelationMapper.selectTeamRelationByBiz(bizId, bizType)).thenReturn(null);

            // When
            List<CrmTeamMember> result = crmTeamMemberService.getTeamMembersByBiz(bizId, bizType);

            // Then
            assertNotNull(result);
            assertEquals(0, result.size());
            verify(crmTeamRelationMapper).selectTeamRelationByBiz(bizId, bizType);
            verify(crmTeamMemberMapper, never()).selectCrmTeamMembersByTeamId(anyLong());
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class EdgeCaseTests {

        @Test
        @DisplayName("添加团队成员时角色为空应使用默认值")
        void testAddTeamMember_NullRole() {
            // Given
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, testUserId)).thenReturn(null);
            when(crmTeamMemberMapper.insertCrmTeamMember(any(CrmTeamMember.class))).thenReturn(1);

            // When
            int result = crmTeamMemberService.addTeamMember(testTeamId, testUserId, null);

            // Then
            assertEquals(1, result);
            verify(crmTeamMemberMapper).insertCrmTeamMember(argThat(member -> 
                "member".equals(member.getRoleType())
            ));
        }

        @Test
        @DisplayName("批量添加时跳过已存在的成员")
        void testBatchAddTeamMembers_SkipExisting() {
            // Given
            List<Long> userIds = Arrays.asList(100L, 101L, 102L);
            
            // 第二个用户已存在
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, 100L)).thenReturn(null);
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, 101L)).thenReturn(testMember);
            when(crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(testTeamId, 102L)).thenReturn(null);
            
            when(crmTeamMemberMapper.insertCrmTeamMember(any(CrmTeamMember.class))).thenReturn(1);

            // When
            int result = crmTeamMemberService.batchAddTeamMembers(testTeamId, userIds, "member");

            // Then
            assertEquals(2, result); // 只添加了2个，跳过了已存在的
            verify(crmTeamMemberMapper, times(2)).insertCrmTeamMember(any(CrmTeamMember.class));
        }
    }
}
