.ep-radio-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin-left: 3px;
  height: var(--ep-radio-button-height);
  // padding: var(--ep-radio-button-padding-vertical) var(--ep-radio-button-padding-horizontal);
  // border: 1px solid var(--ep-radio-button-border-color);
  border-radius: var(--ep-radio-button-border-radius);
  background: var(--ep-radio-button-bg-color);
  color: var(--ep-radio-button-text-color);
  font-size: var(--ep-radio-button-font-size);
  font-weight: var(--ep-radio-button-font-weight);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover:not(.is-disabled) {
    color: var(--ep-radio-button-hover-text-color);
    border-color: var(--ep-radio-button-hover-border-color);
    background: var(--ep-radio-button-hover-bg-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(26, 117, 255, 0.25);
    z-index: 1;
  }

  &.is-active {
    color: var(--ep-radio-button-active-text-color);
    background: var(--ep-radio-button-active-bg-color);
    border-color: var(--ep-radio-button-active-border-color);
    box-shadow: 0 4px 12px rgba(26, 117, 255, 0.35);
    transform: translateY(-1px);
    z-index: 2;

    .ep-radio-button__inner {
      background: var(--ep-radio-button-active-bg-color);
      color: var(--ep-radio-button-active-text-color);
    }
  }

  &.is-disabled {
    cursor: not-allowed;
    color: var(--ep-radio-button-disabled-text-color);
    background-color: var(--ep-radio-button-disabled-bg-color);
    border-color: var(--ep-radio-button-disabled-border-color);
    box-shadow: none;
    transform: none;
  }

  &__original-radio {
    position: absolute;
    opacity: 0;
    outline: none;
    z-index: -1;
  }

  &__inner {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    padding: var(--ep-radio-button-padding-vertical) var(--ep-radio-button-padding-horizontal);
    background: var(--ep-radio-button-bg-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // 当有多个radio-button相邻时的样式
  & + .ep-radio-button {
    margin-left: 1px;
    // border-left: 1px solid var(--ep-border-color-base);
  }

  &:first-child {
    border-radius: var(--ep-radio-button-border-radius) 0 0 var(--ep-radio-button-border-radius);
  }

  &:last-child {
    border-radius: 0 var(--ep-radio-button-border-radius) var(--ep-radio-button-border-radius) 0;
  }
} 