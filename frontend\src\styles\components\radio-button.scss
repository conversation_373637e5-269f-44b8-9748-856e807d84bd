// 修复 radio-button 样式问题
.ep-radio-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin: 0;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 0;
  background: #fff;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  box-sizing: border-box;

  &:hover:not(.is-disabled) {
    color: #409eff;
    border-color: #409eff;
  }

  &.is-active {
    color: #fff;
    background: #409eff;
    border-color: #409eff;
    z-index: 1;

    .ep-radio-button__inner {
      background: transparent;
      color: #fff;
    }
  }

  &.is-disabled {
    cursor: not-allowed;
    color: #c0c4cc;
    background-color: #f5f7fa;
    border-color: #e4e7ed;
  }

  &__original-radio {
    position: absolute;
    opacity: 0;
    outline: none;
    z-index: -1;
  }

  &__inner {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    padding: 8px 16px;
    background: transparent;
    transition: all 0.3s;
    border: none;
    outline: none;
  }

  // 当有多个radio-button相邻时的样式
  & + .ep-radio-button {
    margin-left: -1px;
  }

  &:first-child {
    border-radius: 4px 0 0 4px;
  }

  &:last-child {
    border-radius: 0 4px 4px 0;
  }

  &:first-child:last-child {
    border-radius: 4px;
  }
}

// 确保 radio-group 容器样式正确
.ep-radio-group {
  display: inline-flex;
  align-items: center;
  font-size: 0;
}