package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmTeamMember;

/**
 * 团队成员Service接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
public interface ICrmTeamMemberService 
{
    /**
     * 查询团队成员
     * 
     * @param id 团队成员主键
     * @return 团队成员
     */
    public CrmTeamMember selectCrmTeamMemberById(Long id);

    /**
     * 查询团队成员列表
     * 
     * @param crmTeamMember 团队成员
     * @return 团队成员集合
     */
    public List<CrmTeamMember> selectCrmTeamMemberList(CrmTeamMember crmTeamMember);

    /**
     * 新增团队成员
     * 
     * @param crmTeamMember 团队成员
     * @return 结果
     */
    public int insertCrmTeamMember(CrmTeamMember crmTeamMember);

    /**
     * 修改团队成员
     * 
     * @param crmTeamMember 团队成员
     * @return 结果
     */
    public int updateCrmTeamMember(CrmTeamMember crmTeamMember);

    /**
     * 批量删除团队成员
     * 
     * @param ids 需要删除的团队成员主键集合
     * @return 结果
     */
    public int deleteCrmTeamMemberByIds(Long[] ids);

    /**
     * 删除团队成员信息
     * 
     * @param id 团队成员主键
     * @return 结果
     */
    public int deleteCrmTeamMemberById(Long id);

    // ========== 业务方法 ==========

    /**
     * 根据团队ID查询成员列表
     * 
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    public List<CrmTeamMember> getTeamMembersByTeamId(Long teamId);

    /**
     * 根据用户ID查询所属团队列表
     * 
     * @param userId 用户ID
     * @return 团队成员列表
     */
    public List<CrmTeamMember> getTeamsByUserId(Long userId);

    /**
     * 添加团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param roleType 角色类型
     * @return 结果
     */
    public int addTeamMember(Long teamId, Long userId, String roleType);

    /**
     * 批量添加团队成员
     * 
     * @param teamId 团队ID
     * @param userIds 用户ID列表
     * @param roleType 角色类型
     * @return 结果
     */
    public int batchAddTeamMembers(Long teamId, List<Long> userIds, String roleType);

    /**
     * 更新团队成员角色
     * 
     * @param id 团队成员ID
     * @param roleType 新角色类型
     * @return 结果
     */
    public int updateMemberRole(Long id, String roleType);

    /**
     * 移除团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 结果
     */
    public int removeTeamMember(Long teamId, Long userId);

    /**
     * 批量移除团队成员
     * 
     * @param userIds 用户ID列表
     * @return 结果
     */
    public int batchRemoveTeamMembers(List<Long> userIds);

    /**
     * 根据业务对象获取团队成员
     * 
     * @param bizId 业务对象ID
     * @param bizType 业务类型
     * @return 团队成员列表
     */
    public List<CrmTeamMember> getTeamMembersByBiz(Long bizId, String bizType);

    /**
     * 检查用户是否为团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否为团队成员
     */
    public boolean isTeamMember(Long teamId, Long userId);

    /**
     * 检查用户在团队中的角色
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 角色类型，如果不是成员则返回null
     */
    public String getUserRoleInTeam(Long teamId, Long userId);
}
