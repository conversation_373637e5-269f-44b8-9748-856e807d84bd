package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmTeam;
import com.ruoyi.common.domain.entity.CrmTeamMember;
import com.ruoyi.common.service.ICrmTeamService;
import com.ruoyi.crm.service.ICrmTeamMemberService;
import com.ruoyi.crm.BaseTestCase;

/**
 * CrmTeamMemberController 集成测试类
 * 使用真实的数据库和完整的Spring上下文进行测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@AutoConfigureWebMvc
@DisplayName("团队成员管理控制器集成测试")
class CrmTeamMemberControllerIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmTeamMemberControllerIntegrationTest.class);
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    @Autowired
    private ICrmTeamService teamService;
    
    @Autowired
    private ICrmTeamMemberService teamMemberService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    
    // 测试数据
    private CrmTeam testTeam;
    private CrmTeamMember testMember;
    private Long testUserId = 1L;
    private Long testUserId2 = 2L;

    @BeforeEach
    void setUp() {
        logger.info("=== 开始设置测试环境 ===");
        
        // 设置MockMvc
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .apply(springSecurity())
                .build();
        
        // 创建测试团队
        testTeam = new CrmTeam();
        testTeam.setTeamName("测试团队-" + System.currentTimeMillis());
        testTeam.setDescription("集成测试用团队");
        testTeam.setStatus("0");
        teamService.insertCrmTeam(testTeam);
        
        logger.info("创建测试团队: ID={}, Name={}", testTeam.getId(), testTeam.getTeamName());
    }

    @AfterEach
    void tearDown() {
        logger.info("=== 开始清理测试环境 ===");
        
        try {
            // 清理团队成员
            if (testTeam != null) {
                List<CrmTeamMember> members = teamMemberService.getTeamMembersByTeamId(testTeam.getId());
                for (CrmTeamMember member : members) {
                    teamMemberService.deleteCrmTeamMemberById(member.getId());
                }
                
                // 清理团队
                teamService.deleteCrmTeamById(testTeam.getId());
                logger.info("清理测试团队: ID={}", testTeam.getId());
            }
        } catch (Exception e) {
            logger.warn("清理测试数据时出现异常: {}", e.getMessage());
        }
    }

    @Nested
    @DisplayName("基础CRUD操作测试")
    class BasicCrudTests {

        @Test
        @DisplayName("查询团队成员列表")
        void testGetTeamMemberList() throws Exception {
            // 先添加一个团队成员
            teamMemberService.addTeamMember(testTeam.getId(), testUserId, "member");
            
            MvcResult result = mockMvc.perform(get("/crm/team-member/list")
                    .param("teamId", testTeam.getId().toString())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("团队成员列表响应: {}", responseBody);
            
            TableDataInfo response = objectMapper.readValue(responseBody, TableDataInfo.class);
            assertNotNull(response);
            assertTrue(response.getTotal() > 0);
        }

        @Test
        @DisplayName("根据团队ID查询成员")
        void testGetTeamMembers() throws Exception {
            // 先添加团队成员
            teamMemberService.addTeamMember(testTeam.getId(), testUserId, "leader");
            teamMemberService.addTeamMember(testTeam.getId(), testUserId2, "member");
            
            MvcResult result = mockMvc.perform(get("/crm/team-member/team/{teamId}", testTeam.getId())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("团队成员响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code"));
            
            @SuppressWarnings("unchecked")
            List<Object> members = (List<Object>) response.get("data");
            assertEquals(2, members.size());
        }

        @Test
        @DisplayName("添加团队成员")
        void testAddTeamMember() throws Exception {
            MvcResult result = mockMvc.perform(post("/crm/team-member/add")
                    .param("teamId", testTeam.getId().toString())
                    .param("userId", testUserId.toString())
                    .param("roleType", "member")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("添加团队成员响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code"));
            
            // 验证成员已添加
            List<CrmTeamMember> members = teamMemberService.getTeamMembersByTeamId(testTeam.getId());
            assertEquals(1, members.size());
            assertEquals(testUserId, members.get(0).getUserId());
            assertEquals("member", members.get(0).getRoleType());
        }

        @Test
        @DisplayName("批量添加团队成员")
        void testBatchAddTeamMembers() throws Exception {
            MvcResult result = mockMvc.perform(post("/crm/team-member/batch-add")
                    .param("teamId", testTeam.getId().toString())
                    .param("userIds", testUserId.toString(), testUserId2.toString())
                    .param("roleType", "member")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("批量添加团队成员响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code"));
            
            // 验证成员已添加
            List<CrmTeamMember> members = teamMemberService.getTeamMembersByTeamId(testTeam.getId());
            assertEquals(2, members.size());
        }

        @Test
        @DisplayName("更新成员角色")
        void testUpdateMemberRole() throws Exception {
            // 先添加团队成员
            teamMemberService.addTeamMember(testTeam.getId(), testUserId, "member");
            List<CrmTeamMember> members = teamMemberService.getTeamMembersByTeamId(testTeam.getId());
            Long memberId = members.get(0).getId();
            
            MvcResult result = mockMvc.perform(put("/crm/team-member/{id}/role", memberId)
                    .param("roleType", "leader")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("更新成员角色响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code"));
            
            // 验证角色已更新
            CrmTeamMember updatedMember = teamMemberService.selectCrmTeamMemberById(memberId);
            assertEquals("leader", updatedMember.getRoleType());
        }

        @Test
        @DisplayName("移除团队成员")
        void testRemoveTeamMember() throws Exception {
            // 先添加团队成员
            teamMemberService.addTeamMember(testTeam.getId(), testUserId, "member");
            
            MvcResult result = mockMvc.perform(delete("/crm/team-member/remove")
                    .param("teamId", testTeam.getId().toString())
                    .param("userId", testUserId.toString())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("移除团队成员响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code"));
            
            // 验证成员已移除
            List<CrmTeamMember> members = teamMemberService.getTeamMembersByTeamId(testTeam.getId());
            assertEquals(0, members.size());
        }
    }

    @Nested
    @DisplayName("业务逻辑测试")
    class BusinessLogicTests {

        @Test
        @DisplayName("检查用户团队成员身份")
        void testCheckMembership() throws Exception {
            // 先添加团队成员
            teamMemberService.addTeamMember(testTeam.getId(), testUserId, "leader");
            
            MvcResult result = mockMvc.perform(get("/crm/team-member/check")
                    .param("teamId", testTeam.getId().toString())
                    .param("userId", testUserId.toString())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("检查成员身份响应: {}", responseBody);
            
            JsonNode response = objectMapper.readTree(responseBody);
            assertNotNull(response);
            assertEquals(200, response.get("code").asInt());
            assertTrue(response.get("data").get("isMember").asBoolean());
            assertEquals("leader", response.get("data").get("role").asText());
        }

        @Test
        @DisplayName("根据用户ID查询所属团队")
        void testGetUserTeams() throws Exception {
            // 先添加团队成员
            teamMemberService.addTeamMember(testTeam.getId(), testUserId, "member");
            
            MvcResult result = mockMvc.perform(get("/crm/team-member/user/{userId}", testUserId)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("用户团队列表响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code"));
            
            @SuppressWarnings("unchecked")
            List<Object> teams = (List<Object>) response.get("data");
            assertTrue(teams.size() > 0);
        }

        @Test
        @DisplayName("重复添加团队成员应该失败")
        void testAddDuplicateMember() throws Exception {
            // 先添加团队成员
            teamMemberService.addTeamMember(testTeam.getId(), testUserId, "member");
            
            // 再次添加相同成员应该失败
            MvcResult result = mockMvc.perform(post("/crm/team-member/add")
                    .param("teamId", testTeam.getId().toString())
                    .param("userId", testUserId.toString())
                    .param("roleType", "member")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("重复添加成员响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(500, response.get("code")); // 应该返回错误
        }
    }

    @Nested
    @DisplayName("异常情况测试")
    class ExceptionTests {

        @Test
        @DisplayName("查询不存在的团队成员")
        void testGetNonExistentTeamMembers() throws Exception {
            Long nonExistentTeamId = 99999L;
            
            MvcResult result = mockMvc.perform(get("/crm/team-member/team/{teamId}", nonExistentTeamId)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("查询不存在团队成员响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code"));
            
            @SuppressWarnings("unchecked")
            List<Object> members = (List<Object>) response.get("data");
            assertEquals(0, members.size());
        }

        @Test
        @DisplayName("移除不存在的团队成员")
        void testRemoveNonExistentMember() throws Exception {
            Long nonExistentUserId = 99999L;
            
            MvcResult result = mockMvc.perform(delete("/crm/team-member/remove")
                    .param("teamId", testTeam.getId().toString())
                    .param("userId", nonExistentUserId.toString())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andDo(print())
                    .andReturn();

            String responseBody = result.getResponse().getContentAsString();
            logger.info("移除不存在成员响应: {}", responseBody);
            
            AjaxResult response = objectMapper.readValue(responseBody, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, response.get("code")); // 删除不存在的记录也应该返回成功
        }
    }
}
