<template>
  <div class="quote-export-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>报价单导出</span>
        <div style="float: right">
          <el-button type="primary" @click="showSealDialog = true">添加印章</el-button>
          <el-button link @click="exportQuote">导出报价单</el-button>
        </div>
      </div>
      <div id="quote-content" class="quote-content">
        <h2>报价单</h2>
        <div class="quote-info">
          <p>客户名称：{{ quoteInfo.customerName }}</p>
          <p>报价日期：{{ quoteInfo.date }}</p>
          <p>报价编号：{{ quoteInfo.quoteNo }}</p>
        </div>
        <el-table :data="quoteInfo.items" border style="width: 100%">
          <el-table-column prop="name" label="项目名称" width="180" />
          <el-table-column prop="quantity" label="数量" width="180" />
          <el-table-column prop="price" label="单价" />
          <el-table-column prop="total" label="总价" />
        </el-table>
        <div class="quote-summary">
          <p>总计：{{ quoteInfo.total }} 元</p>
        </div>
        <div v-if="sealImage" class="seal-container">
          <img :src="sealImage" class="seal-image" />
        </div>
      </div>
    </el-card>

    <!-- 印章选择对话框 -->
    <el-dialog title="选择印章" :visible.sync="showSealDialog" width="30%">
      <div class="seal-options">
        <div class="seal-option" v-for="(seal, index) in sealOptions" :key="index" @click="selectSeal(seal)">
          <img :src="seal.url" class="seal-preview" />
          <span>{{ seal.name }}</span>
        </div>
      </div>
      <div class="upload-seal">
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleSealUpload"
          accept="image/*">
          <el-button type="primary">上传自定义印章</el-button>
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export default {
  name: 'QuoteExport',
  data() {
    return {
      quoteInfo: {
        customerName: '示例客户',
        date: '2024-04-23',
        quoteNo: 'QT20240423001',
        items: [
          { name: '产品A', quantity: 2, price: 100, total: 200 },
          { name: '产品B', quantity: 3, price: 150, total: 450 },
          { name: '产品C', quantity: 1, price: 200, total: 200 }
        ],
        total: 850
      },
      showSealDialog: false,
      sealImage: '',
      sealOptions: [
        {
          name: '公司公章',
          url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iNjAiIGN5PSI2MCIgcj0iNTUiIHN0cm9rZT0iI2ZmMDAwMCIgc3Ryb2tlLXdpZHRoPSIyIi8+PHRleHQgeD0iNjAiIHk9IjYwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBhbGlnbm1lbnQtYmFzZWxpbmU9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjZmYwMDAwIj7lj5HnjrDkuI7lj5HnjrA8L3RleHQ+PC9zdmc+'
        },
        {
          name: '财务专用章',
          url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iNjAiIGN5PSI2MCIgcj0iNTUiIHN0cm9rZT0iIzAwMDBmZiIgc3Ryb2tlLXdpZHRoPSIyIi8+PHRleHQgeD0iNjAiIHk9IjYwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBhbGlnbm1lbnQtYmFzZWxpbmU9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMDAwMGZmIj7mnJ/mjqLlnKjlj7c8L3RleHQ+PC9zdmc+'
        }
      ]
    }
  },
  methods: {
    selectSeal(seal) {
      this.sealImage = seal.url
      this.showSealDialog = false
    },
    handleSealUpload(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        this.sealImage = e.target.result
        this.showSealDialog = false
      }
      reader.readAsDataURL(file.raw)
    },
    async exportQuote() {
      try {
        const element = document.getElementById('quote-content')
        const canvas = await html2canvas(element, {
          scale: 2,
          useCORS: true
        })
        
        const imgData = canvas.toDataURL('image/png')
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        })
        
        const imgWidth = 210
        const pageHeight = 297
        const imgHeight = canvas.height * imgWidth / canvas.width
        let heightLeft = imgHeight
        let position = 0
        
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
        
        while (heightLeft >= 0) {
          position = heightLeft - imgHeight
          pdf.addPage()
          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
          heightLeft -= pageHeight
        }
        
        pdf.save('报价单.pdf')
        this.$message.success('报价单导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('报价单导出失败')
      }
    }
  }
}
</script>

<style scoped>
.quote-export-container {
  padding: 20px;
}

.quote-content {
  padding: 20px;
  background: #fff;
  position: relative;
}

.quote-info {
  margin: 20px 0;
}

.quote-summary {
  margin-top: 20px;
  text-align: right;
  font-weight: bold;
}

.seal-container {
  position: absolute;
  right: 50px;
  bottom: 50px;
  z-index: 1;
}

.seal-image {
  width: 120px;
  height: 120px;
  opacity: 0.8;
}

.seal-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.seal-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.seal-option:hover {
  border-color: #409eff;
}

.seal-preview {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}

.upload-seal {
  margin-top: 20px;
  text-align: center;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style> 