import request from '@/utils/request';
import type { LeadConvertData, LeadData, LeadQueryParams } from './types';

// 转化线索
export function convertLead(data: LeadConvertData) {
    return request({
        url: '/crm/leads/convert',
        method: 'post',
        data
    });
}

// 获取线索列表
export function listLeads(params: LeadQueryParams) {
    return request({
        url: '/crm/leads/list',
        method: 'get',
        params
    });
}

// 获取线索详情
export function getLeads(id: number) {
    return request({
        url: `/crm/leads/${id}`,
        method: 'get'
    });
}

// 创建线索
export function createLead(data: Partial<LeadData>) {
    return request({
        url: '/crm/leads',
        method: 'post',
        data
    });
}

// 更新线索
export function updateLead(id: number, data: Partial<LeadData>) {
    return request({
        url: `/crm/leads/${id}`,
        method: 'put',
        data
    });
}

// 删除线索
export function deleteLead(id: number) {
    return request({
        url: `/crm/leads/${id}`,
        method: 'delete'
    });
} 