package com.ruoyi.common.domain.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 通用团队业务关联对象 crm_team_relations
 * 
 * 根据深化团队管理实施方案-V2，实现通用的团队与业务模块关联
 * 支持联系人、线索、客户、商机等多种业务类型与团队的关联
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
public class CrmTeamRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 团队ID (关联crm_teams.id) */
    @Excel(name = "团队ID")
    private Long teamId;

    /** 业务主键ID (例如: crm_contacts.id) */
    @Excel(name = "业务主键ID")
    private Long bizId;

    /** 业务类型 (例如: CONTACT, LEAD, CUSTOMER, OPPORTUNITY) */
    @Excel(name = "业务类型")
    private String bizType;

    /** 关联状态：0-正常，1-停用 */
    @Excel(name = "关联状态", readConverterExp = "0=正常,1=停用")
    private String relationStatus;

    // 关联查询字段
    /** 团队名称 */
    private String teamName;
    
    /** 团队负责人姓名 */
    private String leaderName;
    
    /** 业务对象名称（根据业务类型动态获取） */
    private String bizName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    public Long getTeamId() 
    {
        return teamId;
    }

    public void setBizId(Long bizId) 
    {
        this.bizId = bizId;
    }

    public Long getBizId() 
    {
        return bizId;
    }

    public void setBizType(String bizType) 
    {
        this.bizType = bizType;
    }

    public String getBizType() 
    {
        return bizType;
    }

    public void setRelationStatus(String relationStatus) 
    {
        this.relationStatus = relationStatus;
    }

    public String getRelationStatus() 
    {
        return relationStatus;
    }

    public void setTeamName(String teamName) 
    {
        this.teamName = teamName;
    }

    public String getTeamName() 
    {
        return teamName;
    }

    public void setLeaderName(String leaderName) 
    {
        this.leaderName = leaderName;
    }

    public String getLeaderName() 
    {
        return leaderName;
    }

    public void setBizName(String bizName) 
    {
        this.bizName = bizName;
    }

    public String getBizName() 
    {
        return bizName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teamId", getTeamId())
            .append("bizId", getBizId())
            .append("bizType", getBizType())
            .append("relationStatus", getRelationStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("teamName", getTeamName())
            .append("leaderName", getLeaderName())
            .append("bizName", getBizName())
            .toString();
    }

    /**
     * 业务类型枚举
     */
    public static class BizType {
        /** 联系人 */
        public static final String CONTACT = "CONTACT";
        /** 线索 */
        public static final String LEAD = "LEAD";
        /** 客户 */
        public static final String CUSTOMER = "CUSTOMER";
        /** 商机 */
        public static final String OPPORTUNITY = "OPPORTUNITY";
        /** 合同 */
        public static final String CONTRACT = "CONTRACT";
        /** 拜访计划 */
        public static final String VISIT_PLAN = "VISIT_PLAN";
    }

    /**
     * 关联状态枚举
     */
    public static class RelationStatus {
        /** 正常 */
        public static final String NORMAL = "0";
        /** 停用 */
        public static final String DISABLED = "1";
    }
}
