$--colors: (
  "primary": (
    "base": green,
  ),
  "success": (
    "base": #21ba45,
  ),
  "warning": (
    "base": #f2711c,
  ),
  "danger": (
    "base": #db2828,
  ),
  "error": (
    "base": #db2828,
  ),
  "info": (
    "base": #42b8dd,
  ),
);

// we can add this to custom namespace, default is 'el'
@forward "element-plus/theme-chalk/src/mixins/config.scss" with (
  $namespace: "ep"
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward "element-plus/theme-chalk/src/common/var.scss" with (
  // do not use same name, it will override.
  $colors: $--colors,
  $button-padding-horizontal: ("default": 50px)
);

// if you want to import all
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;

// TODO: 主题切换功能暂时屏蔽，后续需要重新实现
// custom dark variables
// @use "./dark.scss";
