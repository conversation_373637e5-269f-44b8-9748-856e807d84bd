package com.ruoyi.crm.service.impl;

import java.util.List;
import java.util.Date;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.domain.entity.CrmTeamMember;
import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.common.mapper.CrmTeamMemberMapper;
import com.ruoyi.common.mapper.CrmTeamRelationMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.service.ICrmTeamMemberService;

/**
 * 团队成员Service业务层处理
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@Service
public class CrmTeamMemberServiceImpl implements ICrmTeamMemberService 
{
    @Autowired
    private CrmTeamMemberMapper crmTeamMemberMapper;

    @Autowired
    private CrmTeamRelationMapper crmTeamRelationMapper;

    /**
     * 查询团队成员
     * 
     * @param id 团队成员主键
     * @return 团队成员
     */
    @Override
    public CrmTeamMember selectCrmTeamMemberById(Long id)
    {
        return crmTeamMemberMapper.selectCrmTeamMemberById(id);
    }

    /**
     * 查询团队成员列表
     * 
     * @param crmTeamMember 团队成员
     * @return 团队成员
     */
    @Override
    public List<CrmTeamMember> selectCrmTeamMemberList(CrmTeamMember crmTeamMember)
    {
        return crmTeamMemberMapper.selectCrmTeamMemberList(crmTeamMember);
    }

    /**
     * 新增团队成员
     * 
     * @param crmTeamMember 团队成员
     * @return 结果
     */
    @Override
    public int insertCrmTeamMember(CrmTeamMember crmTeamMember)
    {
        crmTeamMember.setCreateBy(SecurityUtils.getUsername());
        crmTeamMember.setJoinTime(new Date());
        if (crmTeamMember.getStatus() == null) {
            crmTeamMember.setStatus("0"); // 默认正常状态
        }
        if (crmTeamMember.getRoleType() == null) {
            crmTeamMember.setRoleType("member"); // 默认成员角色
        }
        return crmTeamMemberMapper.insertCrmTeamMember(crmTeamMember);
    }

    /**
     * 修改团队成员
     * 
     * @param crmTeamMember 团队成员
     * @return 结果
     */
    @Override
    public int updateCrmTeamMember(CrmTeamMember crmTeamMember)
    {
        crmTeamMember.setUpdateBy(SecurityUtils.getUsername());
        return crmTeamMemberMapper.updateCrmTeamMember(crmTeamMember);
    }

    /**
     * 批量删除团队成员
     * 
     * @param ids 需要删除的团队成员主键
     * @return 结果
     */
    @Override
    public int deleteCrmTeamMemberByIds(Long[] ids)
    {
        return crmTeamMemberMapper.deleteCrmTeamMemberByIds(ids);
    }

    /**
     * 删除团队成员信息
     * 
     * @param id 团队成员主键
     * @return 结果
     */
    @Override
    public int deleteCrmTeamMemberById(Long id)
    {
        return crmTeamMemberMapper.deleteCrmTeamMemberById(id);
    }

    // ========== 业务方法 ==========

    /**
     * 根据团队ID查询成员列表
     * 
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    @Override
    public List<CrmTeamMember> getTeamMembersByTeamId(Long teamId)
    {
        return crmTeamMemberMapper.selectCrmTeamMembersByTeamId(teamId);
    }

    /**
     * 根据用户ID查询所属团队列表
     * 
     * @param userId 用户ID
     * @return 团队成员列表
     */
    @Override
    public List<CrmTeamMember> getTeamsByUserId(Long userId)
    {
        CrmTeamMember query = new CrmTeamMember();
        query.setUserId(userId);
        query.setStatus("0"); // 只查询正常状态的
        return crmTeamMemberMapper.selectCrmTeamMemberList(query);
    }

    /**
     * 添加团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param roleType 角色类型
     * @return 结果
     */
    @Override
    @Transactional
    public int addTeamMember(Long teamId, Long userId, String roleType)
    {
        // 检查是否已经是团队成员
        CrmTeamMember existing = crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(teamId, userId);
        if (existing != null) {
            throw new RuntimeException("用户已经是团队成员");
        }

        CrmTeamMember member = new CrmTeamMember();
        member.setTeamId(teamId);
        member.setUserId(userId);
        member.setRoleType(roleType != null ? roleType : "member");
        member.setJoinTime(new Date());
        member.setStatus("0");
        member.setCreateBy(SecurityUtils.getUsername());

        return crmTeamMemberMapper.insertCrmTeamMember(member);
    }

    /**
     * 批量添加团队成员
     * 
     * @param teamId 团队ID
     * @param userIds 用户ID列表
     * @param roleType 角色类型
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAddTeamMembers(Long teamId, List<Long> userIds, String roleType)
    {
        int count = 0;
        String createBy = SecurityUtils.getUsername();
        Date joinTime = new Date();

        for (Long userId : userIds) {
            // 检查是否已经是团队成员
            CrmTeamMember existing = crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(teamId, userId);
            if (existing == null) {
                CrmTeamMember member = new CrmTeamMember();
                member.setTeamId(teamId);
                member.setUserId(userId);
                member.setRoleType(roleType != null ? roleType : "member");
                member.setJoinTime(joinTime);
                member.setStatus("0");
                member.setCreateBy(createBy);

                count += crmTeamMemberMapper.insertCrmTeamMember(member);
            }
        }

        return count;
    }

    /**
     * 更新团队成员角色
     * 
     * @param id 团队成员ID
     * @param roleType 新角色类型
     * @return 结果
     */
    @Override
    public int updateMemberRole(Long id, String roleType)
    {
        CrmTeamMember member = new CrmTeamMember();
        member.setId(id);
        member.setRoleType(roleType);
        member.setUpdateBy(SecurityUtils.getUsername());
        return crmTeamMemberMapper.updateCrmTeamMember(member);
    }

    /**
     * 移除团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int removeTeamMember(Long teamId, Long userId)
    {
        return crmTeamMemberMapper.deleteCrmTeamMemberByTeamIdAndUserId(teamId, userId);
    }

    /**
     * 批量移除团队成员
     * 
     * @param userIds 用户ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchRemoveTeamMembers(List<Long> userIds)
    {
        int count = 0;
        for (Long userId : userIds) {
            CrmTeamMember query = new CrmTeamMember();
            query.setUserId(userId);
            List<CrmTeamMember> members = crmTeamMemberMapper.selectCrmTeamMemberList(query);
            for (CrmTeamMember member : members) {
                count += crmTeamMemberMapper.deleteCrmTeamMemberById(member.getId());
            }
        }
        return count;
    }

    /**
     * 根据业务对象获取团队成员
     * 
     * @param bizId 业务对象ID
     * @param bizType 业务类型
     * @return 团队成员列表
     */
    @Override
    public List<CrmTeamMember> getTeamMembersByBiz(Long bizId, String bizType)
    {
        // 先查询业务对象关联的团队
        CrmTeamRelation relation = crmTeamRelationMapper.selectTeamRelationByBiz(bizId, bizType);
        if (relation == null) {
            return new ArrayList<>();
        }

        // 再查询团队成员
        return crmTeamMemberMapper.selectCrmTeamMembersByTeamId(relation.getTeamId());
    }

    /**
     * 检查用户是否为团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否为团队成员
     */
    @Override
    public boolean isTeamMember(Long teamId, Long userId)
    {
        CrmTeamMember member = crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(teamId, userId);
        return member != null && "0".equals(member.getStatus());
    }

    /**
     * 检查用户在团队中的角色
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 角色类型，如果不是成员则返回null
     */
    @Override
    public String getUserRoleInTeam(Long teamId, Long userId)
    {
        CrmTeamMember member = crmTeamMemberMapper.selectCrmTeamMemberByTeamIdAndUserId(teamId, userId);
        return (member != null && "0".equals(member.getStatus())) ? member.getRoleType() : null;
    }
}
