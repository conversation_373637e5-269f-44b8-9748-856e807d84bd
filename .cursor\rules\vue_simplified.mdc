---
description: 
globs: 
alwaysApply: true
---
# Vue 项目开发规范

## 1. 项目结构规范
### 1.1 目录结构
```
frontend/
├── src/
│   ├── api/
│   ├── assets/
│   ├── components/
│   ├── composables/
│   ├── config/
│   ├── layout/
│   ├── plugins/
│   ├── router/
│   ├── store/
│   ├── styles/
│   ├── types/
│   ├── utils/
│   └── views/
```
### 1.2 文件命名规范
- 组件文件：PascalCase
- 路由页面：kebab - case
- 工具函数：camelCase
- API 文件：模块名称
- 类型定义：PascalCase 加 `.d.ts` 或 `.ts` 后缀

## 2. 编码规范
### 2.1 Vue 组件规范
- 使用 Composition API 和 `<script setup>`
- 组件名 PascalCase
- Props 定义含类型和默认值
- 用 `defineEmits` 定义事件，`defineProps` 定义属性

### 2.2 TypeScript 规范
- 新文件用 TypeScript
- 定义接口或类型，避免 `any`
- 用 `type` 定义函数类型，`interface` 定义对象类型

### 2.3 样式规范
- 使用 SCSS
- BEM 命名
- 组件样式 `scoped`
- 统一管理主题变量和响应式断点

## 3. API 接口规范
### 3.1 接口定义
定义接口结构和获取接口数据方法

### 3.2 请求封装
统一请求函数，定义参数和响应类型，统一错误处理

## 4. 状态管理规范
- 按模块拆分 store
- 定义完整类型
- 用组合式 API 风格，避免直接修改 state

## 5. 组件开发规范
### 5.1 公共组件
- 完整类型定义
- 有使用文档
- 处理默认值
- 事件命名 kebab - case

### 5.2 业务组件
- 单一职责
- 保持纯度
- 避免过度封装
- 合理拆分

## 6. 性能优化规范
### 6.1 代码分割
- 路由懒加载
- 组件按需加载
- 第三方库按需引入