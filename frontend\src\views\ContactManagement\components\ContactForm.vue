<!-- 联系人表单组件 -->
<template>
  <!-- 使用 Element Plus 的表单组件 -->
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    class="contact-form"
  >
    <!-- 基本信息卡片 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
        </div>
      </template>
      
      <!-- 第一行：姓名和客户名称 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerName">
            <!-- 远程搜索的客户选择器 -->
            <el-select 
              v-model="form.customerName"
              filterable
              remote
              placeholder="请选择客户"
              :remote-method="handleCustomerSearch"
              :loading="loading"
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 第二行：职务和性别 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职务" prop="position">
            <el-input v-model="form.position" placeholder="请输入职务"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="form.gender">
              <el-radio label="1">男</el-radio>
              <el-radio label="2">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：手机号码和电话号码 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入手机号码"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话号码" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入电话号码"/>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：电子邮件和关键决策人 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="电子邮件" prop="email">
            <el-input v-model="form.email" placeholder="请输入电子邮件"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关键决策人" prop="isKeyDecisionMaker">
            <el-switch
              v-model="form.isKeyDecisionMaker"
              active-value="1"
              inactive-value="0"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第五行：直属上级 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="直属上级" prop="directSuperior">
            <el-input v-model="form.directSuperior" placeholder="请输入直属上级"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 地址信息卡片 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>地址信息</span>
        </div>
      </template>

      <!-- 地址选择行 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-cascader
              v-model="form.address"
              :options="addressOptions"
              placeholder="请选择地址"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="详细地址" prop="detailedAddress">
            <el-input v-model="form.detailedAddress" placeholder="请输入详细地址"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 跟进信息卡片 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>跟进信息</span>
        </div>
      </template>

      <!-- 负责人和下次联系时间 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="responsiblePersonId">
            <el-select
              v-model="form.responsiblePersonId"
              placeholder="请选择负责人"
            >
              <el-option
                v-for="item in responsiblePersonOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下次联系" prop="nextContactTime">
            <el-date-picker
              v-model="form.nextContactTime"
              type="datetime"
              placeholder="请选择下次联系时间"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注信息 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注信息" prop="remarks">
            <el-input
              v-model="form.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表单底部按钮 -->
    <div class="form-footer">
      <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
// 导入必要的类型和组件
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { addContacts, getResponsiblePersonList, searchCustomers, updateContacts } from '../api/contact'
import type { CrmContacts, CustomerOption, ResponsiblePersonOption } from '../types'

// 初始化路由实例
const router = useRouter()
// 表单引用
const formRef = ref<FormInstance>()
// 加载状态
const loading = ref(false)
// 客户选项列表
const customerOptions = ref<CustomerOption[]>([])
// 地址选项列表
const addressOptions = ref([])
// 负责人选项列表
const responsiblePersonOptions = ref<ResponsiblePersonOption[]>([])

// 表单数据对象
const form = reactive<CrmContacts>({
  name: '',
  customerName: '',
  position: '',
  gender: '',
  mobile: '',
  phone: '',
  email: '',
  isKeyDecisionMaker: '0',
  directSuperior: '',
  address: '',
  detailedAddress: '',
  responsiblePersonId: '',
  nextContactTime: '',
  remarks: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  customerName: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  mobile: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  responsiblePersonId: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
})

/**
 * 处理客户搜索
 * @param query 搜索关键词
 */
const handleCustomerSearch = async (query: string) => {
  if (query) {
    loading.value = true
    try {
      const res = await searchCustomers(query)
      customerOptions.value = res.data.map((item: any) => ({
        value: item.customerName,
        label: item.customerName
      }))
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      loading.value = false
    }
  }
}

/**
 * 获取负责人列表
 */
const getResponsiblePersons = async () => {
  try {
    const res = await getResponsiblePersonList()
    responsiblePersonOptions.value = res.data.map((item: any) => ({
      value: item.userId,
      label: item.userName
    }))
  } catch (error) {
    console.error('获取负责人列表失败:', error)
  }
}

/**
 * 提交表单
 * @param formEl 表单实例
 */
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        if (form.id) {
          await updateContacts(form)
        } else {
          await addContacts(form)
        }
        ElMessage.success('保存成功')
        router.push('/contact-management/list')
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      }
    }
  })
}

/**
 * 取消操作，返回列表页
 */
const cancel = () => {
  router.push('/contact-management/list')
}

// 组件挂载时获取负责人列表
onMounted(() => {
  getResponsiblePersons()
})
</script>

<style scoped>
/* 表单整体样式 */
.contact-form {
  padding: 20px;
}

/* 底部间距 */
.mb-4 {
  margin-bottom: 16px;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

/* 表单底部按钮区域样式 */
.form-footer {
  margin-top: 20px;
  text-align: center;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

/* 表单项样式 */
.el-form-item {
  margin-bottom: 18px;
}
</style> 