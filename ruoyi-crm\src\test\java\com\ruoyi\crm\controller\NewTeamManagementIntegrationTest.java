package com.ruoyi.crm.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmTeam;
import com.ruoyi.common.service.ICrmTeamService;
import com.ruoyi.crm.service.ICrmTeamMemberService;
import com.ruoyi.crm.service.ICrmTeamRelationService;

/**
 * 新团队管理架构集成测试
 * 
 * 测试新的通用团队管理架构是否正常工作
 * 包括团队关联、团队成员管理等功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Transactional
public class NewTeamManagementIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmTeamService teamService;

    @Autowired
    private ICrmTeamMemberService teamMemberService;

    @Autowired
    private ICrmTeamRelationService teamRelationService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private CrmTeam testTeam;
    private Long testContactId = 1L;
    private Long testUserId = 1L;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 创建测试团队
        testTeam = new CrmTeam();
        testTeam.setTeamName("测试团队");
        testTeam.setTeamCode("TEST_TEAM");
        testTeam.setTeamType("SALES");
        testTeam.setLeaderId(testUserId);
        testTeam.setStatus("0");
        testTeam.setCreateBy("test");
        
        // 保存测试团队
        teamService.insertCrmTeam(testTeam);
    }

    @Test
    @DisplayName("测试团队关联功能")
    void testTeamRelation() throws Exception {
        // 1. 分配联系人到团队
        mockMvc.perform(post("/crm/relation/assign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(new Object() {
                    public Long teamId = testTeam.getId();
                    public Long bizId = testContactId;
                    public String bizType = "CONTACT";
                })))
                .andExpect(status().isOk())
                .andDo(print());

        // 2. 查询联系人所属团队
        mockMvc.perform(get("/crm/relation/team")
                .param("bizId", testContactId.toString())
                .param("bizType", "CONTACT"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.teamId").value(testTeam.getId()))
                .andDo(print());

        // 3. 取消分配
        mockMvc.perform(post("/crm/relation/unassign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(new Object() {
                    public Long bizId = testContactId;
                    public String bizType = "CONTACT";
                })))
                .andExpect(status().isOk())
                .andDo(print());
    }

    @Test
    @DisplayName("测试团队成员管理功能")
    void testTeamMemberManagement() throws Exception {
        // 1. 添加团队成员
        mockMvc.perform(post("/crm/team-member/add")
                .param("teamId", testTeam.getId().toString())
                .param("userId", testUserId.toString())
                .param("roleType", "member"))
                .andExpect(status().isOk())
                .andDo(print());

        // 2. 查询团队成员列表
        mockMvc.perform(get("/crm/team-member/team/" + testTeam.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andDo(print());

        // 3. 根据业务对象查询团队成员
        // 先分配联系人到团队
        teamRelationService.assignTeamToBiz(testTeam.getId(), testContactId, "CONTACT");
        
        mockMvc.perform(get("/crm/team-member/biz")
                .param("bizId", testContactId.toString())
                .param("bizType", "CONTACT"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andDo(print());

        // 4. 移除团队成员
        mockMvc.perform(delete("/crm/team-member/remove")
                .param("teamId", testTeam.getId().toString())
                .param("userId", testUserId.toString()))
                .andExpect(status().isOk())
                .andDo(print());
    }

    @Test
    @DisplayName("测试权限检查功能")
    void testPermissionCheck() throws Exception {
        // 添加团队成员
        teamMemberService.addTeamMember(testTeam.getId(), testUserId, "admin");
        
        // 检查用户是否为团队成员
        mockMvc.perform(get("/crm/team-member/check")
                .param("teamId", testTeam.getId().toString())
                .param("userId", testUserId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.isMember").value(true))
                .andExpect(jsonPath("$.data.role").value("admin"))
                .andDo(print());
    }

    @Test
    @DisplayName("测试批量操作功能")
    void testBatchOperations() throws Exception {
        // 批量添加团队成员
        Long[] userIds = {1L, 2L, 3L};
        
        mockMvc.perform(post("/crm/team-member/batch-add")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(new Object() {
                    public Long teamId = testTeam.getId();
                    public Long[] userIds = new Long[]{1L, 2L, 3L};
                    public String roleType = "member";
                })))
                .andExpect(status().isOk())
                .andDo(print());

        // 验证成员已添加
        mockMvc.perform(get("/crm/team-member/team/" + testTeam.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.length()").value(3))
                .andDo(print());
    }
}
