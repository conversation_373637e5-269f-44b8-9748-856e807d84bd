<template>
  <div class="radio-button-test">
    <h2>Radio Button 测试页面</h2>
    
    <div class="test-section">
      <h3>基础用法</h3>
      <el-radio-group v-model="viewMode1" size="small">
        <el-radio-button value="list">列表</el-radio-button>
        <el-radio-button value="grid">网格</el-radio-button>
      </el-radio-group>
      <p>当前选择: {{ viewMode1 }}</p>
    </div>

    <div class="test-section">
      <h3>三个选项</h3>
      <el-radio-group v-model="viewMode2" size="small">
        <el-radio-button value="table">表格</el-radio-button>
        <el-radio-button value="card">卡片</el-radio-button>
        <el-radio-button value="timeline">时间线</el-radio-button>
      </el-radio-group>
      <p>当前选择: {{ viewMode2 }}</p>
    </div>

    <div class="test-section">
      <h3>不同尺寸</h3>
      <div class="size-test">
        <div>
          <label>Large:</label>
          <el-radio-group v-model="viewMode3" size="large">
            <el-radio-button value="list">列表</el-radio-button>
            <el-radio-button value="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
        <div>
          <label>Default:</label>
          <el-radio-group v-model="viewMode3">
            <el-radio-button value="list">列表</el-radio-button>
            <el-radio-button value="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
        <div>
          <label>Small:</label>
          <el-radio-group v-model="viewMode3" size="small">
            <el-radio-button value="list">列表</el-radio-button>
            <el-radio-button value="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>禁用状态</h3>
      <el-radio-group v-model="viewMode4" size="small">
        <el-radio-button value="list">列表</el-radio-button>
        <el-radio-button value="grid" disabled>网格(禁用)</el-radio-button>
        <el-radio-button value="card">卡片</el-radio-button>
      </el-radio-group>
    </div>

    <div class="test-section">
      <h3>实际使用场景 - 团队成员视图</h3>
      <el-card class="members-card">
        <template #header>
          <div class="card-header">
            <span>团队成员 (3人)</span>
            <div class="header-actions">
              <el-radio-group v-model="teamViewMode" size="small">
                <el-radio-button value="list">列表</el-radio-button>
                <el-radio-button value="grid">网格</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="demo-content">
          <p>当前视图模式: {{ teamViewMode }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const viewMode1 = ref('list')
const viewMode2 = ref('table')
const viewMode3 = ref('list')
const viewMode4 = ref('list')
const teamViewMode = ref('list')
</script>

<style scoped>
.radio-button-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.test-section h3 {
  margin-top: 0;
  color: #303133;
}

.size-test > div {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.size-test label {
  width: 80px;
  font-weight: 500;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.demo-content {
  padding: 20px;
  text-align: center;
  color: #666;
}
</style>
