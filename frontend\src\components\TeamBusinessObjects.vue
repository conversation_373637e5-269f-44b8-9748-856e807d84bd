<template>
  <div class="team-business-objects">
    <!-- 头部统计 -->
    <div class="statistics-section">
      <el-row :gutter="16">
        <el-col :span="6" v-for="stat in statistics" :key="stat.type">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" :class="stat.iconClass">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.count }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-row :gutter="16" align="middle">
        <el-col :span="6">
          <el-select v-model="filterType" placeholder="选择业务类型" clearable @change="handleFilter">
            <el-option label="全部类型" value="" />
            <el-option label="联系人" value="CONTACT" />
            <el-option label="线索" value="LEAD" />
            <el-option label="客户" value="CUSTOMER" />
            <el-option label="商机" value="OPPORTUNITY" />
            <el-option label="合同" value="CONTRACT" />
            <el-option label="拜访计划" value="VISIT_PLAN" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索业务对象名称"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateFilter"
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 业务对象列表 -->
    <div class="objects-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>关联的业务对象 ({{ filteredObjects.length }})</span>
            <div class="header-actions">
              <el-button-group>
                <el-button 
                  :type="viewMode === 'table' ? 'primary' : 'default'" 
                  size="small"
                  @click="viewMode = 'table'"
                >
                  <el-icon><Grid /></el-icon>
                  表格
                </el-button>
                <el-button 
                  :type="viewMode === 'card' ? 'primary' : 'default'" 
                  size="small"
                  @click="viewMode = 'card'"
                >
                  <el-icon><Postcard /></el-icon>
                  卡片
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-table :data="filteredObjects" v-loading="loading" style="width: 100%">
            <el-table-column prop="bizName" label="名称" width="200">
              <template #default="{ row }">
                <el-button link @click="viewObjectDetail(row)">
                  {{ row.bizName }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="bizType" label="类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getBizTypeTagType(row.bizType)" size="small">
                  {{ getBizTypeLabel(row.bizType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="assignTime" label="分配时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.assignTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="assignBy" label="分配人" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'info'" size="small">
                  {{ row.status === 'active' ? '活跃' : '非活跃' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button link size="small" @click="viewObjectDetail(row)">
                  查看
                </el-button>
                <el-button
                  link
                  size="small"
                  style="color: #f56c6c;"
                  @click="unassignObject(row)"
                >
                  取消分配
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="16">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="obj in filteredObjects" :key="obj.id">
              <div class="object-card" @click="viewObjectDetail(obj)">
                <div class="card-header">
                  <div class="object-type">
                    <el-tag :type="getBizTypeTagType(obj.bizType)" size="small">
                      {{ getBizTypeLabel(obj.bizType) }}
                    </el-tag>
                  </div>
                  <div class="card-actions">
                    <el-dropdown @command="(cmd: string) => handleCardAction(cmd, obj)">
                      <el-button link size="small">
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="view">查看详情</el-dropdown-item>
                          <el-dropdown-item command="unassign" divided>取消分配</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
                <div class="card-body">
                  <h4 class="object-name">{{ obj.bizName }}</h4>
                  <p class="assign-info">
                    分配人：{{ obj.assignBy }}
                  </p>
                  <p class="assign-time">
                    {{ formatDate(obj.assignTime) }}
                  </p>
                </div>
                <div class="card-footer">
                  <el-tag :type="obj.status === 'active' ? 'success' : 'info'" size="small">
                    {{ obj.status === 'active' ? '活跃' : '非活跃' }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredObjects.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无关联的业务对象">
            <el-button type="primary" @click="refreshData">刷新数据</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getBizsByTeam,
  getTeamStatistics,
  unassignTeamFromBiz
} from '@/api/team-relation'
import {
  Briefcase,
  Grid,
  MoreFilled,
  Phone,
  Postcard,
  Search,
  Shop,
  User
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'

// 定义接口
interface BusinessObject {
  id: number
  bizId: number
  bizType: string
  bizName: string
  assignTime: string
  assignBy: string
  status: string
}

interface Statistic {
  type: string
  label: string
  count: number
  icon: any
  iconClass: string
}

interface Props {
  teamId: number
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const viewMode = ref<'table' | 'card'>('table')
const businessObjects = ref<BusinessObject[]>([])
const statistics = ref<Statistic[]>([])

// 筛选和搜索
const filterType = ref('')
const searchKeyword = ref('')
const dateRange = ref<[string, string] | undefined>(undefined)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 计算属性
const filteredObjects = computed(() => {
  let result = businessObjects.value

  // 类型筛选
  if (filterType.value) {
    result = result.filter(obj => obj.bizType === filterType.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(obj => 
      obj.bizName.toLowerCase().includes(keyword) ||
      obj.assignBy.toLowerCase().includes(keyword)
    )
  }

  // 日期筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    result = result.filter(obj => {
      const assignDate = obj.assignTime.split(' ')[0]
      return assignDate >= startDate && assignDate <= endDate
    })
  }

  return result
})

// 方法
const loadBusinessObjects = async () => {
  loading.value = true
  try {
    const response = await getBizsByTeam(props.teamId)
    businessObjects.value = response.data?.rows || response.data || []
    total.value = response.data?.total || businessObjects.value.length
  } catch (error) {
    console.error('加载业务对象失败:', error)
    ElMessage.error('加载业务对象失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getTeamStatistics()
    const stats = response.data || {}
    
    statistics.value = [
      {
        type: 'CONTACT',
        label: '联系人',
        count: stats.CONTACT || 0,
        icon: User,
        iconClass: 'contact-icon'
      },
      {
        type: 'LEAD',
        label: '线索',
        count: stats.LEAD || 0,
        icon: Phone,
        iconClass: 'lead-icon'
      },
      {
        type: 'CUSTOMER',
        label: '客户',
        count: stats.CUSTOMER || 0,
        icon: Shop,
        iconClass: 'customer-icon'
      },
      {
        type: 'OPPORTUNITY',
        label: '商机',
        count: stats.OPPORTUNITY || 0,
        icon: Briefcase,
        iconClass: 'opportunity-icon'
      }
    ]
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const viewObjectDetail = (obj: BusinessObject) => {
  // 根据业务类型跳转到对应的详情页面
  console.log('查看业务对象详情:', obj)
  ElMessage.info('跳转到详情页面功能开发中...')
}

const unassignObject = async (obj: BusinessObject) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消 "${obj.bizName}" 的团队分配吗？`,
      '确认取消分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await unassignTeamFromBiz(obj.bizId, obj.bizType)
    ElMessage.success('取消分配成功')
    
    // 重新加载数据
    await loadBusinessObjects()
    await loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消分配失败:', error)
      ElMessage.error('取消分配失败')
    }
  }
}

const handleCardAction = (command: string, obj: BusinessObject) => {
  if (command === 'view') {
    viewObjectDetail(obj)
  } else if (command === 'unassign') {
    unassignObject(obj)
  }
}

const handleFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const handleDateFilter = () => {
  // 日期筛选逻辑在计算属性中处理
}

const resetFilters = () => {
  filterType.value = ''
  searchKeyword.value = ''
  dateRange.value = undefined
}

const refreshData = () => {
  loadBusinessObjects()
  loadStatistics()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadBusinessObjects()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadBusinessObjects()
}

// 辅助方法
const getBizTypeTagType = (bizType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    CONTACT: 'primary',
    LEAD: 'warning',
    CUSTOMER: 'success',
    OPPORTUNITY: 'info',
    CONTRACT: 'danger',
    VISIT_PLAN: 'info'
  }
  return typeMap[bizType] || 'info'
}

const getBizTypeLabel = (bizType: string) => {
  const labelMap: Record<string, string> = {
    CONTACT: '联系人',
    LEAD: '线索',
    CUSTOMER: '客户',
    OPPORTUNITY: '商机',
    CONTRACT: '合同',
    VISIT_PLAN: '拜访计划'
  }
  return labelMap[bizType] || '未知'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadBusinessObjects()
  loadStatistics()
})

// 监听团队ID变化
watch(() => props.teamId, () => {
  if (props.teamId) {
    loadBusinessObjects()
    loadStatistics()
  }
})
</script>

<style scoped>
.team-business-objects {
  padding: 20px;
}

/* 统计卡片 */
.statistics-section {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.contact-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.lead-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.customer-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.opportunity-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* 对象列表 */
.objects-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 表格视图 */
.table-view {
  margin-top: 16px;
}

/* 卡片视图 */
.card-view {
  margin-top: 16px;
}

.object-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.object-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.object-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.object-card:hover .card-actions {
  opacity: 1;
}

.card-body {
  margin-bottom: 12px;
}

.object-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.assign-info,
.assign-time {
  margin: 4px 0;
  color: #909399;
  font-size: 14px;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 0;
}

/* 分页 */
.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .team-business-objects {
    padding: 12px;
  }

  .filter-section .el-row {
    flex-direction: column;
  }

  .filter-section .el-col {
    margin-bottom: 8px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
