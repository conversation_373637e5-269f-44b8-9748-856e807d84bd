<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmTeamMemberMapper">
    
    <resultMap type="CrmTeamMember" id="CrmTeamMemberResult">
        <result property="id"           column="id"           />
        <result property="teamId"       column="team_id"      />
        <result property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="roleType"     column="role_type"    />
        <result property="joinTime"     column="join_time"    />
        <result property="status"       column="status"       />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
        <!-- 关联查询字段 -->
        <result property="teamName"     column="team_name"    />
    </resultMap>

    <sql id="selectCrmTeamMemberVo">
        select tm.id, tm.team_id, tm.user_id, tm.user_name, tm.nick_name, tm.role_type, 
               tm.join_time, tm.status, tm.create_by, tm.create_time, tm.update_by, tm.update_time, tm.remark,
               t.team_name
        from crm_team_members tm
        left join crm_teams t on tm.team_id = t.id
    </sql>

    <select id="selectCrmTeamMemberList" parameterType="CrmTeamMember" resultMap="CrmTeamMemberResult">
        <include refid="selectCrmTeamMemberVo"/>
        <where>  
            <if test="teamId != null"> and tm.team_id = #{teamId}</if>
            <if test="userId != null"> and tm.user_id = #{userId}</if>
            <if test="userName != null and userName != ''"> and tm.user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null and nickName != ''"> and tm.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="roleType != null and roleType != ''"> and tm.role_type = #{roleType}</if>
            <if test="status != null and status != ''"> and tm.status = #{status}</if>
        </where>
        order by tm.join_time desc
    </select>
    
    <select id="selectCrmTeamMemberById" parameterType="Long" resultMap="CrmTeamMemberResult">
        <include refid="selectCrmTeamMemberVo"/>
        where tm.id = #{id}
    </select>

    <select id="selectTeamMembersByTeamId" parameterType="Long" resultMap="CrmTeamMemberResult">
        <include refid="selectCrmTeamMemberVo"/>
        where tm.team_id = #{teamId} and tm.status = '0'
        order by tm.role_type, tm.join_time
    </select>

    <select id="selectTeamMembersByUserId" parameterType="Long" resultMap="CrmTeamMemberResult">
        <include refid="selectCrmTeamMemberVo"/>
        where tm.user_id = #{userId} and tm.status = '0'
        order by tm.join_time desc
    </select>

    <select id="selectCrmTeamMembersByTeamId" parameterType="Long" resultMap="CrmTeamMemberResult">
        <include refid="selectCrmTeamMemberVo"/>
        where tm.team_id = #{teamId} and tm.status = '0'
        order by tm.role_type, tm.join_time
    </select>

    <select id="selectCrmTeamMemberByTeamIdAndUserId" resultMap="CrmTeamMemberResult">
        <include refid="selectCrmTeamMemberVo"/>
        where tm.team_id = #{teamId} and tm.user_id = #{userId}
    </select>

    <select id="checkTeamMemberExists" resultType="int">
        select count(1) from crm_team_members
        where team_id = #{teamId} and user_id = #{userId}
    </select>

    <insert id="insertCrmTeamMember" parameterType="CrmTeamMember" useGeneratedKeys="true" keyProperty="id">
        insert into crm_team_members
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId != null">team_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="roleType != null and roleType != ''">role_type,</if>
            <if test="joinTime != null">join_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamId != null">#{teamId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="roleType != null and roleType != ''">#{roleType},</if>
            <if test="joinTime != null">#{joinTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateCrmTeamMember" parameterType="CrmTeamMember">
        update crm_team_members
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="roleType != null and roleType != ''">role_type = #{roleType},</if>
            <if test="joinTime != null">join_time = #{joinTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmTeamMemberById" parameterType="Long">
        delete from crm_team_members where id = #{id}
    </delete>

    <delete id="deleteCrmTeamMemberByIds" parameterType="String">
        delete from crm_team_members where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCrmTeamMemberByTeamIdAndUserId">
        delete from crm_team_members
        where team_id = #{teamId} and user_id = #{userId}
    </delete>

    <delete id="removeTeamMember">
        delete from crm_team_members
        where team_id = #{teamId} and user_id = #{userId}
    </delete>

    <insert id="batchInsertTeamMembers">
        insert into crm_team_members (team_id, user_id, user_name, nick_name, role_type, status, create_by, create_time)
        values 
        <foreach collection="members" item="member" separator=",">
            (#{member.teamId}, #{member.userId}, #{member.userName}, #{member.nickName}, 
             #{member.roleType}, #{member.status}, #{member.createBy}, sysdate())
        </foreach>
    </insert>

    <update id="updateMemberRole">
        update crm_team_members 
        set role_type = #{roleType}, update_by = #{updateBy}, update_time = sysdate()
        where team_id = #{teamId} and user_id = #{userId}
    </update>

</mapper>
