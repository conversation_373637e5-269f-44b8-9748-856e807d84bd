<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmTeamRelationMapper">
    
    <resultMap type="CrmTeamRelation" id="CrmTeamRelationResult">
        <result property="id"               column="id"               />
        <result property="teamId"           column="team_id"          />
        <result property="bizId"            column="biz_id"           />
        <result property="bizType"          column="biz_type"         />
        <result property="relationStatus"   column="relation_status"  />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
        <!-- 关联查询字段 -->
        <result property="teamName"         column="team_name"        />
        <result property="leaderName"       column="leader_name"      />
        <result property="bizName"          column="biz_name"         />
    </resultMap>

    <sql id="selectCrmTeamRelationVo">
        select tr.id, tr.team_id, tr.biz_id, tr.biz_type, tr.relation_status, 
               tr.create_by, tr.create_time, tr.update_by, tr.update_time, tr.remark,
               t.team_name, u.nick_name as leader_name
        from crm_team_relations tr
        left join crm_teams t on tr.team_id = t.id
        left join sys_user u on t.leader_id = u.user_id
    </sql>

    <select id="selectCrmTeamRelationList" parameterType="CrmTeamRelation" resultMap="CrmTeamRelationResult">
        <include refid="selectCrmTeamRelationVo"/>
        <where>  
            <if test="teamId != null "> and tr.team_id = #{teamId}</if>
            <if test="bizId != null "> and tr.biz_id = #{bizId}</if>
            <if test="bizType != null  and bizType != ''"> and tr.biz_type = #{bizType}</if>
            <if test="relationStatus != null  and relationStatus != ''"> and tr.relation_status = #{relationStatus}</if>
        </where>
        order by tr.create_time desc
    </select>
    
    <select id="selectCrmTeamRelationById" parameterType="Long" resultMap="CrmTeamRelationResult">
        <include refid="selectCrmTeamRelationVo"/>
        where tr.id = #{id}
    </select>

    <select id="selectTeamIdByBiz" resultType="Long">
        select team_id from crm_team_relations 
        where biz_id = #{bizId} and biz_type = #{bizType} and relation_status = '0'
        limit 1
    </select>

    <select id="selectTeamRelationByBiz" resultMap="CrmTeamRelationResult">
        <include refid="selectCrmTeamRelationVo"/>
        where tr.biz_id = #{bizId} and tr.biz_type = #{bizType} and tr.relation_status = '0'
        limit 1
    </select>

    <select id="selectBizListByTeam" resultMap="CrmTeamRelationResult">
        <include refid="selectCrmTeamRelationVo"/>
        where tr.team_id = #{teamId} and tr.relation_status = '0'
        <if test="bizType != null and bizType != ''">
            and tr.biz_type = #{bizType}
        </if>
        order by tr.create_time desc
    </select>

    <select id="checkBizAssigned" resultType="int">
        select count(1) from crm_team_relations 
        where biz_id = #{bizId} and biz_type = #{bizType} and relation_status = '0'
    </select>

    <select id="countBizByTeam" resultType="int">
        select count(1) from crm_team_relations 
        where team_id = #{teamId} and relation_status = '0'
        <if test="bizType != null and bizType != ''">
            and biz_type = #{bizType}
        </if>
    </select>

    <select id="countBizByTeamAndType" resultMap="CrmTeamRelationResult">
        select tr.team_id, tr.biz_type, count(1) as biz_id, t.team_name
        from crm_team_relations tr
        left join crm_teams t on tr.team_id = t.id
        where tr.relation_status = '0'
        <if test="bizType != null and bizType != ''">
            and tr.biz_type = #{bizType}
        </if>
        group by tr.team_id, tr.biz_type, t.team_name
        order by count(1) desc
    </select>
        
    <insert id="insertCrmTeamRelation" parameterType="CrmTeamRelation" useGeneratedKeys="true" keyProperty="id">
        insert into crm_team_relations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId != null">team_id,</if>
            <if test="bizId != null">biz_id,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="relationStatus != null">relation_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamId != null">#{teamId},</if>
            <if test="bizId != null">#{bizId},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="relationStatus != null">#{relationStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <insert id="assignBizToTeam">
        insert into crm_team_relations (team_id, biz_id, biz_type, relation_status, create_by, create_time)
        values (#{teamId}, #{bizId}, #{bizType}, '0', #{createBy}, sysdate())
        on duplicate key update 
            team_id = #{teamId}, 
            relation_status = '0', 
            update_by = #{createBy}, 
            update_time = sysdate()
    </insert>

    <insert id="batchAssignBizToTeam">
        insert into crm_team_relations (team_id, biz_id, biz_type, relation_status, create_by, create_time)
        values 
        <foreach collection="bizIds" item="bizId" separator=",">
            (#{teamId}, #{bizId}, #{bizType}, '0', #{createBy}, sysdate())
        </foreach>
        on duplicate key update 
            team_id = #{teamId}, 
            relation_status = '0', 
            update_by = #{createBy}, 
            update_time = sysdate()
    </insert>

    <update id="updateCrmTeamRelation" parameterType="CrmTeamRelation">
        update crm_team_relations
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="bizId != null">biz_id = #{bizId},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="relationStatus != null">relation_status = #{relationStatus},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmTeamRelationById" parameterType="Long">
        delete from crm_team_relations where id = #{id}
    </delete>

    <delete id="deleteCrmTeamRelationByIds" parameterType="String">
        delete from crm_team_relations where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="unassignBizFromTeam">
        delete from crm_team_relations 
        where biz_id = #{bizId} and biz_type = #{bizType}
    </delete>

    <delete id="batchUnassignBizFromTeam">
        delete from crm_team_relations 
        where biz_type = #{bizType} and biz_id in
        <foreach collection="bizIds" item="bizId" open="(" separator="," close=")">
            #{bizId}
        </foreach>
    </delete>

</mapper>
