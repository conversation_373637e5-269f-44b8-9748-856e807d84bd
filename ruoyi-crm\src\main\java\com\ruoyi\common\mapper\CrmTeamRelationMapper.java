package com.ruoyi.common.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.common.domain.entity.CrmTeamRelation;

/**
 * 通用团队业务关联Mapper接口
 * 
 * 根据深化团队管理实施方案-V2，实现通用的团队与业务模块关联
 * 支持联系人、线索、客户、商机等多种业务类型与团队的关联
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@Mapper
public interface CrmTeamRelationMapper 
{
    /**
     * 查询通用团队业务关联
     * 
     * @param id 通用团队业务关联主键
     * @return 通用团队业务关联
     */
    public CrmTeamRelation selectCrmTeamRelationById(Long id);

    /**
     * 查询通用团队业务关联列表
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 通用团队业务关联集合
     */
    public List<CrmTeamRelation> selectCrmTeamRelationList(CrmTeamRelation crmTeamRelation);

    /**
     * 新增通用团队业务关联
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 结果
     */
    public int insertCrmTeamRelation(CrmTeamRelation crmTeamRelation);

    /**
     * 修改通用团队业务关联
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 结果
     */
    public int updateCrmTeamRelation(CrmTeamRelation crmTeamRelation);

    /**
     * 删除通用团队业务关联
     * 
     * @param id 通用团队业务关联主键
     * @return 结果
     */
    public int deleteCrmTeamRelationById(Long id);

    /**
     * 批量删除通用团队业务关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmTeamRelationByIds(Long[] ids);

    /**
     * 根据业务对象查询关联的团队ID
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 团队ID，如果未关联则返回null
     */
    public Long selectTeamIdByBiz(@Param("bizId") Long bizId, @Param("bizType") String bizType);

    /**
     * 根据业务对象查询关联的团队信息
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 团队关联信息
     */
    public CrmTeamRelation selectTeamRelationByBiz(@Param("bizId") Long bizId, @Param("bizType") String bizType);

    /**
     * 根据团队ID查询关联的业务对象列表
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选，为空则查询所有类型）
     * @return 业务关联列表
     */
    public List<CrmTeamRelation> selectBizListByTeam(@Param("teamId") Long teamId, @Param("bizType") String bizType);

    /**
     * 分配业务对象到团队
     * 
     * @param teamId 团队ID
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @param createBy 创建者
     * @return 结果
     */
    public int assignBizToTeam(@Param("teamId") Long teamId, @Param("bizId") Long bizId, 
                               @Param("bizType") String bizType, @Param("createBy") String createBy);

    /**
     * 取消业务对象的团队分配
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 结果
     */
    public int unassignBizFromTeam(@Param("bizId") Long bizId, @Param("bizType") String bizType);

    /**
     * 批量分配业务对象到团队
     * 
     * @param teamId 团队ID
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @param createBy 创建者
     * @return 结果
     */
    public int batchAssignBizToTeam(@Param("teamId") Long teamId, @Param("bizIds") List<Long> bizIds, 
                                    @Param("bizType") String bizType, @Param("createBy") String createBy);

    /**
     * 批量取消业务对象的团队分配
     * 
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @return 结果
     */
    public int batchUnassignBizFromTeam(@Param("bizIds") List<Long> bizIds, @Param("bizType") String bizType);

    /**
     * 检查业务对象是否已分配给团队
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 是否已分配（1-已分配，0-未分配）
     */
    public int checkBizAssigned(@Param("bizId") Long bizId, @Param("bizType") String bizType);

    /**
     * 统计团队关联的业务对象数量
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选）
     * @return 关联数量
     */
    public int countBizByTeam(@Param("teamId") Long teamId, @Param("bizType") String bizType);

    /**
     * 根据业务类型统计各团队的业务对象数量
     * 
     * @param bizType 业务类型
     * @return 统计结果列表
     */
    public List<CrmTeamRelation> countBizByTeamAndType(@Param("bizType") String bizType);
}
