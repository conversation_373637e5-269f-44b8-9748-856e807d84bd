{"mcpServers": {"mcp_server_mysql": {"command": "node", "args": ["D:/mcp/mcp-server-mysql/dist/index.js"], "env": {"MYSQL_HOST": "127.0.0.1", "MYSQL_PORT": "3306", "MYSQL_USER": "mycrm41", "MYSQL_PASS": "mycrm41", "MYSQL_DB": "crm41", "MYSQL_ENABLE_LOGGING": "true", "MYSQL_LOG_LEVEL": "info", "MYSQL_METRICS_ENABLED": "true", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "true"}}}}