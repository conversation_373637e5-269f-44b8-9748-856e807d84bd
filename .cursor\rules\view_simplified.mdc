---
description: 
globs: 
alwaysApply: true
---
# CRM 前端页面结构开发规范

## 适用范围
适用于CRM业务模块主页面，基于Vue3 + TypeScript + Element Plus技术栈。

## 1. 目录结构
每个业务页面建议：
```
src/views/模块名/
├── index.vue                # 主页面
├── api/                     # API 封装
│   └── index.ts
├── config/                  # 配置（表格、筛选、抽屉等）
│   ├── index.ts
│   ├── filterConfig.ts
│   └── formConfig.ts
├── tabs/                    # 抽屉内的Tab组件
│   ├── 业务Tab1.vue
│   └── 业务Tab2.vue
├── types/                   # 类型定义
│   └── index.ts
```

## 2. 代码分层与职责
### 2.1 API 层（api/index.ts）
- 封装接口请求，无业务处理。
- 定义接口函数参数和返回类型，命名如`listXxx`等。

### 2.2 配置层（config/）
- 负责静态配置，如表格列等。
- 操作按钮只配置静态部分，事件处理在index.vue。

### 2.3 抽屉Tab组件（tabs/）
- 独立组件，展示抽屉内业务内容，数据由主页面传递。

### 2.4 类型定义（types/）
- 统一存放页面相关类型等，命名如`XxxEntity`等。

### 2.5 主页面（index.vue）
- 负责页面布局、状态管理等业务逻辑。
- 实现按钮事件处理，引入纯配置项。

## 3. 主要页面结构（index.vue）
### 3.1 结构划分
- 导航栏、头部操作区、筛选区、数据表格、分页、抽屉、弹窗。

### 3.2 按钮与操作
- 表格、抽屉操作按钮只配置基本属性，事件处理在index.vue。
- 抽屉Tab菜单配置菜单项。

### 3.3 配置与本地函数分工
- 纯展示配置放config，需交互按钮在index.vue实现事件处理。

## 4. 其他规范
- 严格分层，避免组件、类型等混杂。
- 业务逻辑、事件处理在index.vue，config只做静态配置。
- 完整类型定义，命名符合规范。

## 5. 参考模板
新页面开发参考AssociationManagement目录结构和代码风格。
