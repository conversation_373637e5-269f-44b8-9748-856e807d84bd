<template>
    <div class="main-container">
        <el-row style="margin-top: 20px;">
            <el-col :span="2"></el-col>
            <el-col :span="20">
                <el-card class="content">
                    <!-- 上传前的页面 -->
                    <UploadSection v-if="!isFileUploaded" ref="uploadSection" :file-list="fileList"
                        @upload-success="handleUploadSuccess"
                        @error="(error : any) => ElMessage.error(error.message || '最外层 上传失败')" />

                    <!-- 上传后的页面 -->
                    <div v-else class="quote-section">
                        <!-- 询价单号头部 -->
                        <div class="quote-header">
                            <div class="quote-info">
                                <div class="quote-number">
                                    <span class="label">询价单号：</span>
                                    <span class="value">SQ0011736385222379</span>
                                </div>
                                <div class="quote-name">
                                    <span class="label">名称：</span>
                                    <span class="value">2.外壳</span>
                                </div>
                            </div>
                            <div class="quote-status">
                                <el-tag type="warning">待报价</el-tag>
                            </div>
                        </div>

                        <!-- 表格部分 -->
                        <QuoteTable :table-data="tableData" @selection-change="handleSelectionChange"
                            @model-preview="handleModelPreview" @quantity-change="handleQuantityChange"
                            @remove-row="handleRemoveRow" @open-dialog="openDialog">
                            <el-table-column label="图纸" width="120">
                                <template #default="{ row }">
                                    <el-image style="width: 50px; height: 50px" :src="row.thumbnail"
                                        :preview-src-list="[row.originalUrl]" fit="cover" />
                                </template>
                            </el-table-column>
                        </QuoteTable>

                        <!-- 底部操作区 -->
                        <div class="quote-bottom-section">
                            <!-- 左侧上传区域 -->
                            <div class="upload-more-section">
                                <div class="upload-title">拖拽或者上传更多3D图纸文件或压缩包</div>
                                <el-upload class="upload-more-area" drag action="#" multiple :limit="20"
                                    :on-success="handleUploadSuccess">
                                    <div class="upload-content">
                                        <el-icon class="upload-icon">
                                            <Upload />
                                        </el-icon>
                                        <div class="upload-text">拖拽文件到此处或</div>
                                        <el-button type="primary" class="upload-more-button">
                                            选择3D图纸文件
                                        </el-button>
                                    </div>
                                </el-upload>
                            </div>

                            <!-- 右侧订单信息 -->
                            <div class="order-info-section">
                                <div class="order-details">
                                    <div class="detail-item">
                                        <span class="detail-label">零件数量：</span>
                                        <span class="detail-value">1款，1件</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">预计发货周期：</span>
                                        <span class="detail-value">1个工作日</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">预计发货日期：</span>
                                        <span class="detail-value">2025-01-17 23:00前</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">含税加工费：</span>
                                        <span class="detail-value price">¥{{ totalAmount }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">运费：</span>
                                        <span class="detail-value">(到付)</span>
                                    </div>
                                    <div class="detail-item total">
                                        <span class="detail-label">总计（含税）：</span>
                                        <span class="detail-value price">¥{{ totalAmount }}</span>
                                    </div>
                                </div>
                                <el-button type="primary" class="order-button" @click="handlePlaceOrder">
                                    立即下单
                                </el-button>
                                <div class="order-tips">
                                    若系统报价异常，可先下单，下单后销售经理会与您沟通；同一订单内零件交期不同按最长交期计算；当天18:00以后付款的订单，交期从后一个工作日9：00起算；
                                    约定交期只包含工作日，如遇周日或法定节假日，交期将顺延！订单未满起步价时以起步价结算，如遇疑问，请咨询销售经理。
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="2"></el-col>
        </el-row>
    </div>
    <el-dialog v-model="dialogVisible" width="90%" title="选择打印参数">
        <el-row>
            <el-col :span="4">
                <div class="left-panel">
                    <div class="part-list">
                        <div class="part-item" v-for="model in printModelList" :key="model.id">
                            <el-image :src="model.imageUrl" class="part-thumbnail" fit="cover">
                                <template #error>
                                    <div class="image-slot">
                                        <el-icon>
                                            <Picture />
                                        </el-icon>
                                    </div>
                                </template>
                            </el-image>
                            <div class="part-info">
                                <div class="part-name">{{ model.name }}</div>
                                <div class="part-type" v-if="model.type">
                                    类型: {{ model.type }}
                                </div>
                                <div class="part-specs" v-if="model.techSpecs">
                                    <div v-if="model.techSpecs.材料精度">精度: {{ model.techSpecs.材料精度 }}</div>
                                    <div v-if="model.techSpecs.颜色">颜色: {{ model.techSpecs.颜色 }}</div>
                                </div>
                                <div class="part-price" v-if="model.price">
                                    价格: ¥{{ model.price }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :span="14">
                <div class="material-select">
                    <span>选择材料：</span>
                    <div class="material-buttons">
                        <el-button v-for="type in materialTypes" :key="type" type="primary"
                            :class="{ 'active': selectedMaterial === type }" plain @click="handleMaterialSelect(type)">
                            {{ type }}
                        </el-button>
                    </div>
                </div>
                <div class="table-container">
                    <el-table :data="printParamsTable" class="params-table" height="400" @row-click="handleRowClick"
                        stripe border :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
                        <el-table-column prop="type" label="类型" min-width="100" show-overflow-tooltip align="center">
                            <template #default="scope">
                                <el-tag size="small" type="info">{{ scope.row.type }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="material" label="材料" min-width="120" show-overflow-tooltip align="center">
                            <template #default="scope">
                                <span class="material-name">{{ scope.row.material }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="color" label="颜色" min-width="100" show-overflow-tooltip align="center">
                            <template #default="scope">
                                <el-tag size="small" :type="scope.row.color === '-' ? 'info' : 'success'">
                                    {{ scope.row.color }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="tolerance" label="公差" min-width="100" show-overflow-tooltip align="center">
                            <template #default="scope">
                                <span class="tolerance-text">{{ scope.row.tolerance }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="delivery" label="发货周期" min-width="120" show-overflow-tooltip align="center">
                            <template #default="scope">
                                <el-tag size="small" type="warning">{{ scope.row.delivery }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="price" label="价格" min-width="100" show-overflow-tooltip align="center">
                            <template #default="scope">
                                <span v-if="scope.row.price !== '-'" class="price-text">¥{{ scope.row.price }}</span>
                                <span v-else class="no-price">-</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="features" label="材料特性" min-width="200" show-overflow-tooltip>
                            <template #default="scope">
                                <span class="features-text">{{ scope.row.features }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <!-- 分页组件 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="pageNum"
                            v-model:page-size="pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :small="false"
                            :disabled="loading"
                            :background="true"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="totalItems"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                    
                    <div v-if="loading" class="loading-overlay">
                        <el-icon class="loading-icon">
                            <Loading />
                        </el-icon>
                        <span>加载中...</span>
                    </div>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="right-panel">
                    <!-- 基本信息部分 -->
                    <div class="spec-section basic-info">
                        <div class="product-header">
                            <h3>{{ selectedProduct?.name || '未选择产品' }}</h3>
                            <el-tag size="small" effect="plain">{{ selectedProduct?.type }}</el-tag>
                        </div>

                        <!-- 产品图片轮播 -->
                        <el-carousel v-if="selectedProduct?.imageUrls?.length" height="200px"
                            indicator-position="outside" class="product-carousel">
                            <el-carousel-item v-for="(url, index) in selectedProduct.imageUrls" :key="`carousel-${index}`">
                                <el-image :src="url" fit="contain" class="carousel-image" />
                            </el-carousel-item>
                        </el-carousel>
                        <div v-else class="empty-carousel">
                            <el-icon>
                                <Picture />
                            </el-icon>
                            <span>暂无图片</span>
                        </div>

                        <!-- 基本属性网格 -->
                        <div class="basic-grid">
                            <div class="grid-item">
                                <span class="label">产品编号</span>
                                <span class="value">{{ selectedProduct?.id || '-' }}</span>
                            </div>
                            <div class="grid-item">
                                <span class="label">更新时间</span>
                                <span class="value">{{ formatDate(selectedProduct?.updatedAt) }}</span>
                            </div>
                            <div class="grid-item">
                                <span class="label">价格</span>
                                <span class="value price">{{ selectedProduct?.price ? `¥${selectedProduct.price}` :
                                    '待询价'
                                    }}</span>
                            </div>
                            <div class="grid-item">
                                <span class="label">产品链接</span>
                                <el-link v-if="selectedProduct?.productLink" type="primary"
                                    :href="selectedProduct.productLink" target="_blank">
                                    查看详情
                                </el-link>
                                <span v-else class="value">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
        <el-row>
            <el-col>
                <el-collapse v-model="activeNames" class="custom-collapse">
                    <el-collapse-item name="1" title="选择喷漆，丝印（可选）">
                        <div class="collapse-content">
                            <el-checkbox-group v-model="sprayOptions">
                                <el-checkbox value="喷漆">喷漆</el-checkbox>
                                <el-checkbox value="丝印">丝印</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </el-collapse-item>
                    <el-collapse-item name="2" title="选择镶钢丝牙套，铜螺母（可选）">
                        <div class="collapse-content">
                            <el-checkbox-group v-model="insertOptions">
                                <el-checkbox value="镶钢丝牙套">镶钢丝牙套</el-checkbox>
                                <el-checkbox value="铜螺母">铜螺母</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </el-col>
        </el-row>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="dialogVisible = false">确定</el-button>
            </span>
        </template>
    </el-dialog>

    <ModelViewer ref="modelViewer" :model-info="currentModel" />

</template>

<script lang="ts" setup>
import ModelViewer from '@/components/ModelViewer.vue';
import {
    Loading,
    Picture
} from '@element-plus/icons-vue';
import {
    ElButton,
    ElCard,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElCollapse,
    ElCollapseItem,
    ElDialog,
    ElIcon,
    ElImage,
    ElLink,
    ElMessage,
    ElPagination,
    ElRow,
    ElTable,
    ElTableColumn,
    ElTag,
    ElUpload
} from 'element-plus';
import { computed, onMounted, ref, watch } from 'vue';
import { getPrintModelList, getProductTypes } from '~/api/printModel';
import QuoteTable from './components/QuoteTable.vue';
import UploadSection from './components/UploadSection.vue';

import { PrintModel, Product, TableRow } from '@/types/index';

// 定义响应式变量
const activeNames = ref<string[]>(['1']);
const sprayOptions = ref<string[]>([]);
const insertOptions = ref<string[]>([]);
const tableKey = ref<number>(0);
const fileList = ref<any[]>([]);
const isFileUploaded = ref<boolean>(false);
const tableData = ref<TableRow[]>([
    {
        index: 1,
        drawing: '',
        modelInfo: {
            name: '2.外壳',
            unit: 'mm',
            dimensions: '275.00×103.96×103.92mm',
            volume: '130384.30mm³',
            surfaceArea: '174166.02mm²',
        },
        printParams: '打印参数1',
        quantity: 1,
        unitPrice: 10,
        totalPrice: 10,
    },
]);
const defaultImage = ref<string>('path/to/default-image.jpg');
const selectedRows = ref<any[]>([]);
const dialogVisible = ref<boolean>(false);
const printParamsTable = ref<any[]>([]);
const printModelList = ref<PrintModel[]>([]);
const originalModelList = ref<any[]>([]);
const pageNum = ref<number>(1);
const pageSize = ref<number>(10);
const loading = ref<boolean>(false);
const hasMore = ref<boolean>(true);
const totalItems = ref<number>(0);
const materialTypes = ref<string[]>([]);
const selectedMaterial = ref<string>('');
const selectedProduct = ref<Product | null>(null);
const modelViewer = ref<any>(null);
const currentModel = ref<any>({});

// 计算属性
const hasProcessParams = computed(() => {
    const specs = selectedProduct.value?.techSpecs || {};
    return specs.成型工艺 || specs.材料精度 || specs.颜色;
});

const hasMaterialSpecs = computed(() => {
    const specs = selectedProduct.value?.techSpecs || {};
    return specs.拉伸强度 || specs.拉伸模量 || specs.断裂伸长率 ||
        specs.弯曲模量 || specs.缺口冲击强度 || specs.热变形温度;
});

const hasMaterialInfo = computed(() => {
    return selectedProduct.value?.advantages ||
        selectedProduct.value?.disadvantages ||
        selectedProduct.value?.applicationAreas ||
        selectedProduct.value?.materialProperties;
});

const totalAmount = computed(() => {
    return tableData.value.reduce((sum, item) => {
        return sum + (item.totalPrice || 0);
    }, 0).toFixed(2);
});

// 方法
const handleExceed = (files: File[], fileList: File[]) => {
    ElMessage.warning(
        `当前限制选择 20 个文件，本次选择了 ${files.length} 个文件，共选择了 ${fileList.length} 个文件`
    );
};

const handlePreview = (file: File) => {
    console.log('preview', file);
};

const handleRemove = (file: File, fileList: File[]) => {
    console.log('remove', file, fileList);
};

const handleUploadSuccess = (uploadData: any) => {
    console.log('[3DPrintingQuote] handleUploadSuccess processing', JSON.stringify(uploadData, null, 2));

    const newRow: TableRow = {
        index: tableData.value.length + 1,
        drawing: uploadData.thumbnail,
        modelInfo: {
            name: uploadData.processedFile.name,
            unit: 'mm',
            dimensions: uploadData.processedFile.dimensions,
            volume: `${uploadData.processedFile.volume}mm³`,
            surfaceArea: `${uploadData.processedFile.surfaceArea}mm²`
        },
        printParams: '打印参数1',
        quantity: 1,
        unitPrice: 10,
        totalPrice: 10,
        thumbnail: uploadData.thumbnail,
        originalUrl: uploadData.url
    };

    tableData.value.push(newRow);
    isFileUploaded.value = true;

    console.log('[3DPrintingQuote] handleUploadSuccess completed', {
        newTableData: tableData.value,
        isFileUploaded: isFileUploaded.value
    });
};

const handleNewUpload = () => {
    try {
        console.log('handleNewUpload processing', {
            currentFileList: fileList.value,
            currentIsFileUploaded: isFileUploaded.value
        });

        fileList.value = [];
        isFileUploaded.value = false;

        console.log('handleNewUpload completed', {
            newFileList: fileList.value,
            newIsFileUploaded: isFileUploaded.value
        });
    } catch (error) {
        console.error('handleNewUpload error:', error);
        ElMessage.error('重置上传状态出错');
    }
};

const handleRemoveRow = (index: number) => {
    if (typeof index !== 'number' || index < 0 || index >= tableData.value.length) {
        console.error('handleRemoveRow: Invalid index parameter', index);
        ElMessage.error('删除行索引无效');
        return;
    }

    try {
        console.log('handleRemoveRow processing', {
            index: index,
            rowData: tableData.value[index]
        });

        tableData.value.splice(index, 1);

        console.log('handleRemoveRow completed', {
            newTableData: tableData.value
        });
    } catch (error) {
        console.error('handleRemoveRow error:', error);
        ElMessage.error('删除行出错');
    }
};

const handleSelectionChange = (val: any[]) => {
    if (!Array.isArray(val)) {
        console.error('handleSelectionChange: Invalid val parameter', val);
        ElMessage.error('选择行数据无效');
        return;
    }

    try {
        console.log('handleSelectionChange processing', {
            selectedRows: val
        });

        selectedRows.value = val;

        console.log('handleSelectionChange completed', {
            newSelectedRows: selectedRows.value
        });
    } catch (error) {
        console.error('handleSelectionChange error:', error);
        ElMessage.error('处理选择行变化出错');
    }
};

const openDialog = () => {
    try {
        console.log('openDialog processing');
        dialogVisible.value = true;
        console.log('openDialog completed');
    } catch (error) {
        console.error('openDialog error:', error);
        ElMessage.error('打开对话框失败');
    }
};

const fetchPrintModelList = async (isMaterialSelect: boolean = false) => {
    console.log('[API] Fetch Print Model List Start', {
        timestamp: new Date().toISOString(),
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        selectedMaterial: selectedMaterial.value,
        isMaterialSelect: isMaterialSelect
    });

    if (loading.value) {
        return;
    }

    if (!isMaterialSelect && !hasMore.value && pageNum.value > 1) {
        return;
    }

    loading.value = true;
    try {
        const response = await getPrintModelList({
            pageNum: pageNum.value,
            pageSize: pageSize.value,
            type: selectedMaterial.value
        });

        console.log('[API] Fetch Print Model List Response', {
            timestamp: new Date().toISOString(),
            response: response
        });

        if (response.code === 200) {
            const originalData = response.rows;

            const newData = originalData.map((item: any) => {
                const techSpecs = JSON.parse(item.techSpecs);
                return {
                    type: item.type || '-',
                    material: item.name,
                    color: techSpecs.颜色 || '-',
                    tolerance: techSpecs.材料精度 || '-',
                    delivery: '3-5工作日',
                    price: item.price || '-',
                    features: item.materialProperties || '-',
                    name: item.name
                };
            });

            // 分页模式：替换数据而不是追加
            originalModelList.value = originalData;
            printParamsTable.value = newData;
            totalItems.value = response.total || 0;

            hasMore.value = response.rows.length === pageSize.value;

            if (pageNum.value === 1) {
                const randomItems = originalData
                    .map((item: any) => ({
                        id: item.id,
                        name: item.name,
                        type: item.type,
                        imageUrl: JSON.parse(item.imageUrl)[2],
                        price: item.price,
                        techSpecs: JSON.parse(item.techSpecs)
                    }))
                    .sort(() => Math.random() - 0.5)
                    .slice(0, 5);

                printModelList.value = randomItems;
            }
            console.log('[API] Fetch Print Model List Complete', {
                timestamp: new Date().toISOString(),
                totalItems: originalModelList.value.length,
                hasMore: hasMore.value
            });
        } else {
            console.error('[API] Fetch Print Model List Failed', {
                timestamp: new Date().toISOString(),
                error: response.msg || '获取打印模型列表失败'
            });
            ElMessage.error(response.msg || '获取打印模型列表失败');
        }
    } catch (error) {
        console.error('[API] Fetch Print Model List Error', {
            timestamp: new Date().toISOString(),
            error: error
        });
        ElMessage.error('获取打印模型列表失败');
    } finally {
        loading.value = false;
    }
};

const loadMore = () => {
    if (!loading.value && hasMore.value) {
        console.log('触发无限滚动加载');
        loading.value = true;
        pageNum.value += 1;
        fetchPrintModelList(false);
    }
};

const fetchMaterialTypes = async () => {
    console.log('[API] Fetch Material Types Start', {
        timestamp: new Date().toISOString()
    });

    try {
        const response = await getProductTypes();
        console.log('[API] Fetch Material Types Response', {
            timestamp: new Date().toISOString(),
            response: response
        });

        if (response.code === 200) {
            materialTypes.value = response.data;
            console.log('[API] Fetch Material Types Complete', {
                timestamp: new Date().toISOString(),
                types: materialTypes.value
            });
        } else {
            console.error('[API] Fetch Material Types Failed', {
                timestamp: new Date().toISOString(),
                error: response.msg || '获取材料类型失败'
            });
            ElMessage.error(response.msg || '获取材料类型失败');
        }
    } catch (error) {
        console.error('[API] Fetch Material Types Error', {
            timestamp: new Date().toISOString(),
            error: error
        });
        ElMessage.error('获取材料类型失败');
    }
};

const handleMaterialSelect = (type: string) => {
    selectedMaterial.value = selectedMaterial.value === type ? '' : type;
    pageNum.value = 1;
    fetchPrintModelList(true);
};

const handleRowClick = (row: any) => {
    const originalData = originalModelList.value.find((item: any) => item.name === row.material) || {};
    try {
        let imageUrls: string[] = [];
        if (originalData.imageUrl) {
            try {
                const parsedUrls = JSON.parse(originalData.imageUrl);
                imageUrls = Array.isArray(parsedUrls) ? parsedUrls.reverse() : [];
            } catch (e) {
                console.warn('图片URL解析失败:', e);
                imageUrls = [];
            }
        }

        let techSpecs = {};
        if (originalData.techSpecs) {
            try {
                techSpecs = typeof originalData.techSpecs === 'string'
                    ? JSON.parse(originalData.techSpecs)
                    : originalData.techSpecs;
            } catch (e) {
                console.warn('技术规格解析失败:', e);
                techSpecs = {};
            }
        }

        selectedProduct.value = {
            ...originalData,
            techSpecs,
            imageUrls,
            advantages: originalData.advantages || '',
            disadvantages: originalData.disadvantages || '',
            applicationAreas: originalData.applicationAreas || ''
        };
    } catch (error) {
        console.error('产品数据处理失败:', error);
        ElMessage.error('产品数据解析失败');
    }
};

const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
};

const formatProcessText = (text: string | undefined) => {
    if (!text) return '';
    return text.replace(/\n\s+/g, '\n').trim();
};

const handlePlaceOrder = () => {
    try {
        console.log('handlePlaceOrder processing', {
            tableData: tableData.value,
            selectedRows: selectedRows.value
        });

        ElMessage.success('订单提交成功！');

        console.log('handlePlaceOrder completed');
    } catch (error) {
        console.error('handlePlaceOrder error:', error);
        ElMessage.error('订单提交失败，请稍后重试');
    }
};

const handleQuantityChange = (row: TableRow) => {
    if (!row || typeof row !== 'object') {
        console.error('handleQuantityChange: Invalid row parameter', row);
        ElMessage.error('行数据无效');
        return;
    }

    if (typeof row.quantity !== 'number' || typeof row.unitPrice !== 'number') {
        console.error('handleQuantityChange: Invalid quantity or unitPrice', {
            quantity: row.quantity,
            unitPrice: row.unitPrice
        });
        ElMessage.error('数量或单价无效');
        return;
    }

    try {
        console.log('handleQuantityChange processing', {
            rowData: row,
            oldTotalPrice: row.totalPrice
        });

        row.totalPrice = row.quantity * row.unitPrice;

        console.log('handleQuantityChange completed', {
            newTotalPrice: row.totalPrice
        });
    } catch (error) {
        console.error('handleQuantityChange error:', error);
        ElMessage.error('处理数量变化出错');
    }
};

const handleModelPreview = (row: TableRow) => {
    if (!row || typeof row !== 'object') {
        console.error('handleModelPreview: Invalid row parameter', row);
        ElMessage.error('行数据无效');
        return;
    }

    if (!modelViewer.value) {
        console.error('handleModelPreview: ModelViewer component not ready');
        ElMessage.error('模型预览组件未准备好');
        return;
    }

    try {
        console.log('handleModelPreview processing', {
            rowData: row
        });

        currentModel.value = row;
        const modelUrl = row.modelUrl || '/models/1508523 v2.stl';

        if (!modelUrl) {
            console.error('handleModelPreview: No model URL provided');
            ElMessage.error('模型路径无效');
            return;
        }

        modelViewer.value.open(modelUrl);

        console.log('handleModelPreview completed', {
            modelUrl: modelUrl
        });
    } catch (error) {
        console.error('handleModelPreview error:', error);
        ElMessage.error('处理模型预览出错');
    }
};

// 分页事件处理
const handleSizeChange = (newSize: number) => {
    pageSize.value = newSize;
    pageNum.value = 1;
    fetchPrintModelList(true);
};

const handleCurrentChange = (newPage: number) => {
    pageNum.value = newPage;
    fetchPrintModelList();
};

// 监听器
watch(originalModelList, (newValue) => {
    if (newValue.length > 0 && !selectedProduct.value) {
        const firstProduct = newValue[0];
        try {
            selectedProduct.value = {
                ...firstProduct,
                techSpecs: typeof firstProduct.techSpecs === 'string'
                    ? JSON.parse(firstProduct.techSpecs)
                    : firstProduct.techSpecs,
                imageUrls: (firstProduct.imageUrl ? JSON.parse(firstProduct.imageUrl) : []).reverse(),
                advantages: firstProduct.advantages || '',
                disadvantages: firstProduct.disadvantages || '',
                applicationAreas: firstProduct.applicationAreas || ''
            };
        } catch (error) {
            console.error('解析默认产品数据失败:', error);
            ElMessage.error('产品数据解析失败');
        }
    }
}, { immediate: true });

watch(isFileUploaded, (newVal) => {
    console.log('isFileUploaded值变化:', newVal);
});

watch(fileList, (newVal) => {
    console.log('fileList值变化:', newVal);
});

// 生命周期钩子
onMounted(() => {
    console.log('ThreeDPrintingQuote 组件挂载完成');
    console.log('isFileUploaded初始值:', isFileUploaded.value);
    console.log('fileList初始值:', fileList.value);
    console.log('UploadSection组件是否存在:', !!modelViewer.value);

    setTimeout(() => {
        console.log('延时检查 UploadSection:', {
            ref: modelViewer.value,
            dom: document.querySelector('.upload-section')
        });
    }, 1000);

    fetchPrintModelList();
    fetchMaterialTypes();
});
</script>

<style scoped>
.content {
    background-color: white;
}

.main-container {
    height: 100vh;
}

.quote-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(to right, #f8f9fa, #ffffff);
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.quote-info {
    display: flex;
    gap: 24px;
}

.quote-bottom-section {
    margin-top: 20px;
    padding: 20px;
    display: flex;
    gap: 20px;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
}

.upload-more-section {
    flex: 1;
    padding: 20px;
    background: #f8f9fa;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.order-info-section {
    width: 380px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
}

.left-panel {

    padding: 20px;
    padding-top: 40px;
    height: 100%;
    overflow-y: auto;
    background: #f8f9fa;
}

.part-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.part-item {
    display: flex;
    padding: 12px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.part-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.part-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    margin-right: 12px;
    flex-shrink: 0;
}

.part-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.part-name {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
}

.part-type,
.part-specs,
.part-price {
    font-size: 12px;
    color: #606266;
}

.part-specs {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.part-price {
    font-weight: 600;
    color: #409eff;
}

.material-select {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.material-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 8px;
}

.material-buttons .el-button.active {
    background: #409eff;
    color: white;
    border-color: #409eff;
}

.table-container {
    position: relative;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.params-table {
    width: 100%;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.params-table :deep(.el-table__row) {
    transition: all 0.3s ease;
}

.params-table :deep(.el-table__row:hover) {
    background-color: #f8f9fa !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.params-table :deep(.el-table__cell) {
    border-bottom: 1px solid #ebeef5;
    padding: 12px 8px;
}

.params-table :deep(.el-table__header-wrapper .el-table__cell) {
    background: #f5f7fa;
    color: #606266;
    font-weight: 600;
    border-bottom: 2px solid #409eff;
}

/* 表格内容样式 */
.material-name {
    font-weight: 600;
    color: #303133;
}

.tolerance-text {
    color: #606266;
    font-size: 13px;
}

.price-text {
    color: #409eff;
    font-weight: 600;
    font-size: 14px;
}

.no-price {
    color: #909399;
    font-style: italic;
}

.features-text {
    color: #606266;
    font-size: 13px;
    line-height: 1.4;
}

/* 分页容器样式 */
.pagination-container {
    padding: 20px;
    display: flex;
    justify-content: center;
    background: #fafafa;
    border-top: 1px solid #ebeef5;
}

.pagination-container :deep(.el-pagination) {
    background: transparent;
}

.pagination-container :deep(.el-pagination .el-pager li) {
    background: white;
    border: 1px solid #dcdfe6;
    margin: 0 2px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pagination-container :deep(.el-pagination .el-pager li:hover) {
    background: #409eff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.pagination-container :deep(.el-pagination .el-pager li.is-active) {
    background: #409eff;
    color: white;
    border-color: #409eff;
}

.pagination-container :deep(.el-pagination .btn-prev, .el-pagination .btn-next) {
    background: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pagination-container :deep(.el-pagination .btn-prev:hover, .el-pagination .btn-next:hover) {
    background: #409eff;
    color: white;
    transform: translateY(-1px);
}

/* 加载遮罩样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    z-index: 10;
    backdrop-filter: blur(2px);
}

.loading-icon {
    font-size: 32px;
    color: #409eff;
    animation: loading-rotate 2s linear infinite;
}

@keyframes loading-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay span {
    color: #606266;
    font-size: 14px;
    font-weight: 500;
}

.loading-text,
.no-more-text {
    text-align: center;
    padding: 16px;
    color: #909399;
    font-size: 14px;
}

.right-panel {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    background: #f8f9fa;
}

.spec-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.basic-info {
    margin-bottom: 20px;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.product-header h3 {
    margin: 0;
    font-size: 18px;
    color: #303133;
}

/* 产品轮播样式 */
.product-carousel {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
}

.product-carousel :deep(.el-carousel__container) {
    height: 200px;
}

.product-carousel :deep(.el-carousel__item) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
}

.carousel-image {
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.carousel-image :deep(.el-image__inner) {
    border-radius: 8px;
}

.empty-carousel {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border-radius: 8px;
    color: #909399;
    font-size: 14px;
    gap: 8px;
}

.empty-carousel .el-icon {
    font-size: 32px;
}

.basic-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.grid-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.grid-item .label {
    font-size: 12px;
    color: #909399;
    font-weight: 500;
}

.grid-item .value {
    font-size: 14px;
    color: #303133;
}

.grid-item .value.price {
    color: #409eff;
    font-weight: 600;
}

.image-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    color: #909399;
}

.custom-collapse {
    margin-top: 20px;
}

.collapse-content {
    padding: 10px 0;
}

.dialog-footer {
    display: flex;
    gap: 10px;
}

.upload-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
}

.upload-more-area {
    width: 100%;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-icon {
    font-size: 32px;
    color: #409eff;
}

.upload-text {
    color: #606266;
    font-size: 14px;
}

.upload-more-button {
    margin-top: 8px;
}

.order-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #ebeef5;
}

.detail-item.total {
    border-bottom: none;
    font-weight: 600;
    font-size: 16px;
    color: #303133;
    margin-top: 8px;
    padding-top: 16px;
    border-top: 2px solid #409eff;
}

.detail-label {
    color: #606266;
    font-size: 14px;
}

.detail-value {
    color: #303133;
    font-size: 14px;
}

.detail-value.price {
    color: #409eff;
    font-weight: 600;
    font-size: 16px;
}

.order-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

.order-tips {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    text-align: left;
}

.quote-number,
.quote-name {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quote-number .label,
.quote-name .label {
    color: #606266;
    font-size: 14px;
}

.quote-number .value,
.quote-name .value {
    color: #303133;
    font-size: 14px;
    font-weight: 600;
}
</style>
