<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>CRM系统数据字典(详细版)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #444; margin-top: 30px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 30px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .table-desc { font-style: italic; margin-bottom: 5px; }
    </style>
</head>
<body>
    <h1>CRM系统数据字典</h1>
    <p>最后更新时间: <span id="update-time"></span></p>
    
    <script>
        document.getElementById('update-time').textContent = new Date().toLocaleString();
    </script>
</head>
<body>
    <h2>1. 工作流引擎表</h2>
    
    <div class="table-section">
        <h3>1.1 act_evt_log (事件日志表)</h3>
        <div class="table-desc">存储工作流引擎的事件日志</div>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>长度</th>
                    <th>允许空</th>
                    <th>默认值</th>
                    <th>主键</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>LOG_NR_</td>
                    <td>bigint</td>
                    <td>20</td>
                    <td>否</td>
                    <td></td>
                    <td>是</td>
                    <td>日志编号</td>
                </tr>
                <tr>
                    <td>TYPE_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>事件类型</td>
                </tr>
                <tr>
                    <td>PROC_DEF_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>流程定义ID</td>
                </tr>
                <tr>
                    <td>PROC_INST_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>流程实例ID</td>
                </tr>
                <tr>
                    <td>EXECUTION_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>执行ID</td>
                </tr>
                <tr>
                    <td>TASK_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>任务ID</td>
                </tr>
                <tr>
                    <td>TIME_STAMP_</td>
                    <td>timestamp</td>
                    <td></td>
                    <td>否</td>
                    <td>CURRENT_TIMESTAMP</td>
                    <td>否</td>
                    <td>时间戳</td>
                </tr>
                <tr>
                    <td>USER_ID_</td>
                    <td>varchar</td>
                    <td>255</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>用户ID</td>
                </tr>
                <tr>
                    <td>DATA_</td>
                    <td>longblob</td>
                    <td></td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>事件数据</td>
                </tr>
                <tr>
                    <td>LOCK_OWNER_</td>
                    <td>varchar</td>
                    <td>255</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>锁拥有者</td>
                </tr>
                <tr>
                    <td>LOCK_TIME_</td>
                    <td>timestamp</td>
                    <td></td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>锁定时间</td>
                </tr>
                <tr>
                    <td>IS_PROCESSED_</td>
                    <td>tinyint</td>
                    <td>4</td>
                    <td>是</td>
                    <td>0</td>
                    <td>否</td>
                    <td>是否已处理</td>
                </tr>
            </tbody>
        </table>
        
        <h2>act_ge_bytearray 表</h2>
        <p>Activiti引擎的二进制资源表</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>长度</th>
                    <th>允许空</th>
                    <th>默认值</th>
                    <th>主键</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>否</td>
                    <td></td>
                    <td>是</td>
                    <td>主键ID</td>
                </tr>
                <tr>
                    <td>REV_</td>
                    <td>int</td>
                    <td>11</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>版本号</td>
                </tr>
                <tr>
                    <td>NAME_</td>
                    <td>varchar</td>
                    <td>255</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>资源名称</td>
                </tr>
                <tr>
                    <td>DEPLOYMENT_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>部署ID</td>
                </tr>
                <tr>
                    <td>BYTES_</td>
                    <td>longblob</td>
                    <td></td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>二进制数据</td>
                </tr>
                <tr>
                    <td>GENERATED_</td>
                    <td>tinyint</td>
                    <td>4</td>
                    <td>是</td>
                    <td></td>
                    <td>否</td>
                    <td>是否自动生成</td>
                </tr>
    <meta charset="UTF-8">
    <title>CRM系统数据字典</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #444; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .table-name { font-weight: bold; color: #0066cc; }
        .comment { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <h1>CRM系统数据字典</h1>
    <p>生成日期：2024-02-20</p>
    
    <h2>1. 工作流引擎表</h2>

    <div class="table-section">
        <h3>1.1 act_evt_log (事件日志表)</h3>
        <div class="table-desc">存储工作流引擎的事件日志</div>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>长度</th>
                    <th>允许空</th>
                    <th>默认值</th>
                    <th>主键</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>LOG_NR_</td>
                    <td>bigint</td>
                    <td>20</td>
                    <td>否</td>
                    <td></td>
                    <td>是</td>
                    <td>日志编号</td>
                </tr>
                <tr>
                    <td>TYPE_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>事件类型</td>
                </tr>
                <tr>
                    <td>PROC_DEF_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>流程定义ID</td>
                </tr>
                <tr>
                    <td>PROC_INST_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>流程实例ID</td>
                </tr>
                <tr>
                    <td>EXECUTION_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>执行实例ID</td>
                </tr>
                <tr>
                    <td>TASK_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>任务ID</td>
                </tr>
                <tr>
                    <td>TIME_STAMP_</td>
                    <td>timestamp</td>
                    <td>3</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td>时间戳</td>
                </tr>
                <tr>
                    <td>USER_ID_</td>
                    <td>varchar</td>
                    <td>255</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>用户ID</td>
                </tr>
                <tr>
                    <td>DATA_</td>
                    <td>longblob</td>
                    <td></td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>数据</td>
                </tr>
                <tr>
                    <td>LOCK_OWNER_</td>
                    <td>varchar</td>
                    <td>255</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>锁拥有者</td>
                </tr>
                <tr>
                    <td>LOCK_TIME_</td>
                    <td>timestamp</td>
                    <td>3</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>锁定时间</td>
                </tr>
                <tr>
                    <td>IS_PROCESSED_</td>
                    <td>tinyint</td>
                    <td>4</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>是否已处理</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="table-section">
        <h3>1.2 act_ge_bytearray (通用字节数组表)</h3>
        <div class="table-desc">存储流程定义和流程资源文件的二进制数据</div>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>长度</th>
                    <th>允许空</th>
                    <th>默认值</th>
                    <th>主键</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>否</td>
                    <td></td>
                    <td>是</td>
                    <td>主键ID</td>
                </tr>
                <tr>
                    <td>REV_</td>
                    <td>int</td>
                    <td>11</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>版本号</td>
                </tr>
                <tr>
                    <td>NAME_</td>
                    <td>varchar</td>
                    <td>255</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>名称</td>
                </tr>
                <tr>
                    <td>DEPLOYMENT_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>部署ID</td>
                </tr>
                <tr>
                    <td>BYTES_</td>
                    <td>longblob</td>
                    <td></td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>字节数组</td>
                </tr>
                <tr>
                    <td>GENERATED_</td>
                    <td>tinyint</td>
                    <td>4</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>是否生成</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="table-section">
        <h3>1.3 act_ge_property (属性数据表)</h3>
        <div class="table-desc">存储工作流引擎级别的属性数据</div>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>长度</th>
                    <th>允许空</th>
                    <th>默认值</th>
                    <th>主键</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>NAME_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>否</td>
                    <td></td>
                    <td>是</td>
                    <td>属性名称</td>
                </tr>
                <tr>
                    <td>VALUE_</td>
                    <td>varchar</td>
                    <td>300</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>属性值</td>
                </tr>
                <tr>
                    <td>REV_</td>
                    <td>int</td>
                    <td>11</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>版本号</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="table-section">
        <h3>1.4 act_procdef_info (流程定义信息表)</h3>
        <div class="table-desc">存储流程定义的相关信息</div>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>长度</th>
                    <th>允许空</th>
                    <th>默认值</th>
                    <th>主键</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>否</td>
                    <td></td>
                    <td>是</td>
                    <td>主键ID</td>
                </tr>
                <tr>
                    <td>PROC_DEF_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>否</td>
                    <td></td>
                    <td>否</td>
                    <td>流程定义ID</td>
                </tr>
                <tr>
                    <td>REV_</td>
                    <td>int</td>
                    <td>11</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>版本号</td>
                </tr>
                <tr>
                    <td>INFO_JSON_ID_</td>
                    <td>varchar</td>
                    <td>64</td>
                    <td>是</td>
                    <td>NULL</td>
                    <td>否</td>
                    <td>信息JSON ID</td>
                </tr>
            </tbody>
        </table>
    </div>
        <tr>
            <td class="table-name">act_re_deployment</td>
            <td class="comment">部署信息表</td>
            <td>id_, name_, category_, key_, tenant_id_, deploy_time_, engine_version_</td>
        </tr>
        <tr>
            <td class="table-name">act_re_model</td>
            <td class="comment">流程设计模型部署表</td>
            <td>id_, rev_, name_, key_, category_, create_time_, last_update_time_, version_, meta_info_, deployment_id_, editor_source_value_id_, editor_source_extra_value_id_, tenant_id_</td>
        </tr>
        <tr>
            <td class="table-name">act_re_procdef</td>
            <td class="comment">流程定义数据表</td>
            <td>id_, rev_, category_, name_, key_, version_, deployment_id_, resource_name_, dgrm_resource_name_, description_, has_start_form_key_, has_graphical_notation_, suspension_state_, tenant_id_, engine_version_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_deadletter_job</td>
            <td class="comment">无法执行的工作表</td>
            <td>id_, rev_, type_, exclusive_, execution_id_, process_instance_id_, proc_def_id_, exception_stack_id_, exception_msg_, duedate_, repeat_, handler_type_, handler_cfg_, tenant_id_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_event_subscr</td>
            <td class="comment">运行时事件订阅表</td>
            <td>id_, rev_, event_type_, event_name_, execution_id_, proc_inst_id_, activity_id_, configuration_, created_, proc_def_id_, tenant_id_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_execution</td>
            <td class="comment">运行时流程执行实例表</td>
            <td>id_, rev_, proc_inst_id_, business_key_, parent_id_, proc_def_id_, super_exec_, root_proc_inst_id_, act_id_, is_active_, is_concurrent_, is_scope_, is_event_scope_, is_mi_root_, suspension_state_, cached_ent_state_, tenant_id_, name_, start_time_, start_user_id_, lock_time_, is_count_enabled_, evt_subscr_count_, task_count_, job_count_, timer_job_count_, susp_job_count_, deadletter_job_count_, var_count_, id_link_count_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_identitylink</td>
            <td class="comment">运行时用户关系表</td>
            <td>id_, rev_, group_id_, type_, user_id_, task_id_, proc_inst_id_, proc_def_id_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_integration</td>
            <td class="comment">运行时集成表</td>
            <td>id_, execution_id_, process_instance_id_, proc_def_id_, flow_node_id_, created_date_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_job</td>
            <td class="comment">运行时作业表</td>
            <td>id_, rev_, type_, lock_exp_time_, lock_owner_, exclusive_, execution_id_, process_instance_id_, proc_def_id_, retries_, exception_stack_id_, exception_msg_, duedate_, repeat_, handler_type_, handler_cfg_, tenant_id_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_suspended_job</td>
            <td class="comment">暂停作业表</td>
            <td>id_, rev_, type_, exclusive_, execution_id_, process_instance_id_, proc_def_id_, retries_, exception_stack_id_, exception_msg_, duedate_, repeat_, handler_type_, handler_cfg_, tenant_id_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_task</td>
            <td class="comment">运行时任务表</td>
            <td>id_, rev_, execution_id_, proc_inst_id_, proc_def_id_, name_, parent_task_id_, description_, task_def_key_, owner_, assignee_, delegation_, priority_, create_time_, due_date_, category_, suspension_state_, tenant_id_, form_key_, claim_time_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_timer_job</td>
            <td class="comment">定时作业表</td>
            <td>id_, rev_, type_, lock_exp_time_, lock_owner_, exclusive_, execution_id_, process_instance_id_, proc_def_id_, retries_, exception_stack_id_, exception_msg_, duedate_, repeat_, handler_type_, handler_cfg_, tenant_id_</td>
        </tr>
        <tr>
            <td class="table-name">act_ru_variable</td>
            <td class="comment">运行时变量表</td>
            <td>id_, rev_, type_, name_, execution_id_, proc_inst_id_, task_id_, bytearray_id_, double_, long_, text_, text2_</td>
        </tr>
    </table>
    
    <h2>2. CRM核心业务表</h2>
    <table>
        <tr>
            <th>表名</th>
            <th>说明</th>
            <th>主要字段</th>
        </tr>
        <tr>
            <td class="table-name">crm_business</td>
            <td class="comment">商机表</td>
            <td>id, responsible_person_id, name, amount, expected_close_date, stage, probability, source, status</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_ap_approval_history</td>
            <td class="comment">审批历史表</td>
            <td>id, process_instance_id, node_id, approver_id, approval_time, approval_result, approval_opinion, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_ap_approval_process</td>
            <td class="comment">审批流程表</td>
            <td>id, process_name, process_key, process_description, creator_id, status, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_ap_my_application</td>
            <td class="comment">我的申请表</td>
            <td>id, applicant_id, process_instance_id, application_title, application_content, application_time, status, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_ap_node_detail</td>
            <td class="comment">节点详情表</td>
            <td>id, process_id, node_name, node_type, approver_type, approver_ids, node_order, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_ap_process_instance</td>
            <td class="comment">流程实例表</td>
            <td>id, process_id, initiator_id, business_key, start_time, end_time, status, current_node_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_contacts</td>
            <td class="comment">联系人表</td>
            <td>id, responsible_person_id, name, mobile, phone, email, position, is_key_decision_maker, direct_superior</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_contract_user_relations</td>
            <td class="comment">合同用户关系表</td>
            <td>id, contract_id, user_id, relation_type, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_contract_watchlist</td>
            <td class="comment">合同关注列表</td>
            <td>id, contract_id, watcher_id, watch_time, is_active, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_contracts</td>
            <td class="comment">合同表</td>
            <td>id, contract_no, business_id, customer_id, amount, sign_date, effective_date, expiry_date, status</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_customers</td>
            <td class="comment">客户表</td>
            <td>id, name, industry, scale, source, level, address, website, remarks</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_follow_up_records</td>
            <td class="comment">跟进记录表</td>
            <td>id, business_id, contact_id, follow_type, content, next_contact_time, status</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_lead_assignment_history</td>
            <td class="comment">线索分配历史表</td>
            <td>id, lead_id, previous_assignee_id, new_assignee_id, assignment_time, assignment_reason, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_lead_followers</td>
            <td class="comment">线索关注者表</td>
            <td>id, lead_id, follower_id, follow_time, is_active, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_lead_user_associations</td>
            <td class="comment">线索用户关联表</td>
            <td>id, lead_id, user_id, association_type, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_leads</td>
            <td class="comment">线索表</td>
            <td>id, name, company, position, phone, email, source, status, assignee_id</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_opportunities</td>
            <td class="comment">商机表</td>
            <td>id, name, customer_id, expected_amount, expected_close_date, stage, probability, source</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_payment_details</td>
            <td class="comment">付款详情表</td>
            <td>id, payment_id, item_name, item_amount, tax_rate, tax_amount, remarks, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_payment_plans</td>
            <td class="comment">付款计划表</td>
            <td>id, contract_id, plan_name, expected_amount, expected_date, actual_amount, actual_date, status, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_business_payments</td>
            <td class="comment">回款表</td>
            <td>id, contract_id, amount, payment_date, payment_method, status, remarks</td>
        </tr>
        <tr>
            <td class="table-name">crm_contact_followers</td>
            <td class="comment">联系人关注者表</td>
            <td>id, contact_id, follower_id, follow_time, is_active, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_contact_followup_records</td>
            <td class="comment">联系人跟进记录表</td>
            <td>id, contact_id, followup_type, content, followup_time, next_followup_time, creator_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_contract_followup_records</td>
            <td class="comment">合同跟进记录表</td>
            <td>id, contract_id, followup_type, content, followup_time, next_followup_time, creator_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_customer_contact_relations</td>
            <td class="comment">客户联系人关联表</td>
            <td>id, customer_id, contact_id, relation_type, is_primary, status, start_date, end_date</td>
        </tr>
        <tr>
            <td class="table-name">crm_customer_followup_records</td>
            <td class="comment">客户跟进记录表</td>
            <td>id, customer_id, followup_type, content, followup_time, next_followup_time, creator_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_customer_operation_log</td>
            <td class="comment">客户操作日志表</td>
            <td>id, customer_id, operation_type, operation_content, operator_id, operation_time, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_lead_followup_records</td>
            <td class="comment">线索跟进记录表</td>
            <td>id, lead_id, followup_type, content, followup_time, next_followup_time, creator_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_lead_operation_log</td>
            <td class="comment">线索操作日志表</td>
            <td>id, lead_id, operation_type, operation_content, operator_id, operation_time, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_menu</td>
            <td class="comment">CRM菜单表</td>
            <td>menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">crm_opportunity_followup_records</td>
            <td class="comment">商机跟进记录表</td>
            <td>id, opportunity_id, followup_type, content, followup_time, next_followup_time, creator_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_payment_followup_records</td>
            <td class="comment">回款跟进记录表</td>
            <td>id, payment_id, followup_type, content, followup_time, next_followup_time, creator_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_products</td>
            <td class="comment">产品表</td>
            <td>id, name, code, category, specification, unit, price, cost, status</td>
        </tr>
        <tr>
            <td class="table-name">crm_static_files</td>
            <td class="comment">静态文件表</td>
            <td>id, file_name, file_path, file_type, file_size, upload_time, uploader_id, business_type, business_id, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_thirdparty_wechat</td>
            <td class="comment">第三方微信表</td>
            <td>id, open_id, union_id, nick_name, avatar_url, gender, country, province, city, language, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_user_hierarchy</td>
            <td class="comment">用户层级表</td>
            <td>id, user_id, superior_id, hierarchy_level, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">crm_wecom_config</td>
            <td class="comment">企业微信配置表</td>
            <td>id, corp_id, agent_id, secret, token, encoding_aes_key, status, create_time, update_time, del_flag</td>
        </tr>
        <tr>
            <td class="table-name">gen_table</td>
            <td class="comment">代码生成业务表</td>
            <td>table_id, table_name, table_comment, sub_table_name, sub_table_fk_name, class_name, tpl_category, package_name, module_name, business_name, function_name, function_author, gen_type, gen_path, options, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">gen_table_column</td>
            <td class="comment">代码生成业务表字段</td>
            <td>column_id, table_id, column_name, column_comment, column_type, java_type, java_field, is_pk, is_increment, is_required, is_insert, is_edit, is_list, is_query, query_type, html_type, dict_type, sort, create_by, create_time, update_by, update_time</td>
        </tr>
    </table>
    
    <h2>3. 系统管理表</h2>
    <table>
        <tr>
            <th>表名</th>
            <th>说明</th>
            <th>主要字段</th>
        </tr>
        <tr>
            <td class="table-name">sys_config</td>
            <td class="comment">参数配置表</td>
            <td>config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_dept</td>
            <td class="comment">部门表</td>
            <td>dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time, update_by, update_time</td>
        </tr>
        <tr>
            <td class="table-name">sys_dict_data</td>
            <td class="comment">字典数据表</td>
            <td>dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_dict_type</td>
            <td class="comment">字典类型表</td>
            <td>dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_job</td>
            <td class="comment">定时任务调度表</td>
            <td>job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_job_log</td>
            <td class="comment">定时任务调度日志表</td>
            <td>job_log_id, job_name, job_group, invoke_target, job_message, status, exception_info, create_time</td>
        </tr>
        <tr>
            <td class="table-name">sys_logininfor</td>
            <td class="comment">系统访问记录</td>
            <td>info_id, user_name, ipaddr, login_location, browser, os, status, msg, login_time</td>
        </tr>
        <tr>
            <td class="table-name">sys_menu</td>
            <td class="comment">菜单权限表</td>
            <td>menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_notice</td>
            <td class="comment">通知公告表</td>
            <td>notice_id, notice_title, notice_type, notice_content, status, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_oper_log</td>
            <td class="comment">操作日志记录</td>
            <td>oper_id, title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time</td>
        </tr>
        <tr>
            <td class="table-name">sys_post</td>
            <td class="comment">岗位信息表</td>
            <td>post_id, post_code, post_name, post_sort, status, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_role</td>
            <td class="comment">角色信息表</td>
            <td>role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_role_dept</td>
            <td class="comment">角色和部门关联表</td>
            <td>role_id, dept_id</td>
        </tr>
        <tr>
            <td class="table-name">sys_role_menu</td>
            <td class="comment">角色和菜单关联表</td>
            <td>role_id, menu_id</td>
        </tr>
        <tr>
            <td class="table-name">sys_user</td>
            <td class="comment">用户信息表</td>
            <td>user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark</td>
        </tr>
        <tr>
            <td class="table-name">sys_user_post</td>
            <td class="comment">用户与岗位关联表</td>
            <td>user_id, post_id</td>
        </tr>
        <tr>
            <td class="table-name">sys_user_role</td>
            <td class="comment">用户和角色关联表</td>
            <td>user_id, role_id</td>
        </tr>
    </table>
    
    <h2>4. Quartz调度表</h2>
    <table>
        <tr>
            <th>表名</th>
            <th>说明</th>
            <th>主要字段</th>
        </tr>
        <tr>
            <td class="table-name">qrtz_blob_triggers</td>
            <td class="comment">Blob类型的触发器表</td>
            <td>sched_name, trigger_name, trigger_group, blob_data</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_calendars</td>
            <td class="comment">日历信息表</td>
            <td>sched_name, calendar_name, calendar</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_cron_triggers</td>
            <td class="comment">Cron类型的触发器表</td>
            <td>sched_name, trigger_name, trigger_group, cron_expression, time_zone_id</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_fired_triggers</td>
            <td class="comment">已触发的触发器表</td>
            <td>sched_name, entry_id, trigger_name, trigger_group, instance_name, fired_time, sched_time, priority, state</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_job_details</td>
            <td class="comment">任务详细信息表</td>
            <td>sched_name, job_name, job_group, description, job_class_name, is_durable, is_nonconcurrent, is_update_data, requests_recovery, job_data</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_locks</td>
            <td class="comment">存储的悲观锁信息表</td>
            <td>sched_name, lock_name</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_paused_trigger_grps</td>
            <td class="comment">暂停的触发器表</td>
            <td>sched_name, trigger_group</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_scheduler_state</td>
            <td class="comment">调度器状态表</td>
            <td>sched_name, instance_name, last_checkin_time, checkin_interval</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_simple_triggers</td>
            <td class="comment">简单触发器的信息表</td>
            <td>sched_name, trigger_name, trigger_group, repeat_count, repeat_interval, times_triggered</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_simprop_triggers</td>
            <td class="comment">同步机制的行锁表</td>
            <td>sched_name, trigger_name, trigger_group, str_prop_1, str_prop_2, str_prop_3, int_prop_1, int_prop_2, long_prop_1, long_prop_2, dec_prop_1, dec_prop_2, bool_prop_1, bool_prop_2</td>
        </tr>
        <tr>
            <td class="table-name">qrtz_triggers</td>
            <td class="comment">触发器详细信息表</td>
            <td>sched_name, trigger_name, trigger_group, job_name, job_group, description, next_fire_time, prev_fire_time, priority, trigger_state, trigger_type, start_time, end_time, calendar_name, misfire_instr, job_data</td>
        </tr>
    </table>
    
    <h2>5. 视图</h2>
    <table>
        <tr>
            <th>视图名</th>
            <th>说明</th>
            <th>主要字段</th>
        </tr>
        <tr>
            <td class="table-name">v_customer_contacts</td>
            <td class="comment">客户联系人视图</td>
            <td>customer_id, customer_name, contact_id, contact_name, mobile, email, position, relation_type, is_primary</td>
        </tr>
        <tr>
            <td class="table-name">v_user_followed_contacts_stats</td>
            <td class="comment">用户关注联系人统计视图</td>
            <td>follower_id, followed_count, active_followed_count, latest_follow_time</td>
        </tr>
        <tr>
            <td class="table-name">v_user_hierarchy_relations</td>
            <td class="comment">用户层级关系视图</td>
            <td>user_id, superior_id, user_name, superior_name, hierarchy_level</td>
        </tr>
    </table>
</body>
</html>