<template>
  <el-dialog 
    :model-value="props.visible"
    :title="title" 
    width="600px"
    @close="handleClose"
  >
    <div class="team-assign-dialog">
      <!-- 当前分配状态 -->
      <div v-if="currentTeam" class="current-assignment">
        <el-alert
          :title="`当前已分配给团队：${currentTeam.teamName}`"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="current-team-info">
              <p>团队负责人：{{ currentTeam.leaderName || '未设置' }}</p>
              <p>分配时间：{{ formatDate(currentTeam.createTime) }}</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 分配表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="选择团队" prop="teamId">
          <el-select 
            v-model="form.teamId" 
            placeholder="请选择要分配的团队" 
            style="width: 100%;"
            filterable
            @focus="loadTeams"
          >
            <el-option
              v-for="team in availableTeams"
              :key="team.id"
              :label="team.teamName"
              :value="team.id"
              :disabled="team.id === currentTeam?.teamId"
            >
              <div class="team-option">
                <span class="team-name">{{ team.teamName }}</span>
                <span class="team-leader">{{ team.leaderName || '无负责人' }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分配原因" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入分配原因（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 批量分配时显示选中的对象 -->
        <el-form-item v-if="isBatchMode" label="分配对象">
          <div class="batch-items">
            <el-tag
              v-for="item in batchItems"
              :key="item.id"
              closable
              @close="removeBatchItem(item.id)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ item.name }}
            </el-tag>
          </div>
          <p class="batch-count">共 {{ batchItems.length }} 个对象</p>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button v-if="currentTeam" @click="handleUnassign" :loading="unassigning">
          取消分配
        </el-button>
        <el-button type="primary" @click="handleAssign" :loading="assigning">
          {{ currentTeam ? '重新分配' : '分配团队' }}
        </el-button>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="assigning">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  getTeamByBiz, 
  assignTeamToBiz, 
  unassignTeamFromBiz,
  batchAssignTeamToBiz 
} from '@/api/team-relation'

// 定义接口
interface Team {
  id: number
  teamName: string
  leaderName?: string
  description?: string
}

interface TeamRelation {
  teamId: number
  teamName: string
  leaderName?: string
  createTime: string
}

interface BatchItem {
  id: number
  name: string
}

interface Props {
  visible: boolean
  bizId?: number
  bizType?: string
  bizIds?: number[]
  bizNames?: string[]
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '分配团队'
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
  'close': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const availableTeams = ref<Team[]>([])
const currentTeam = ref<TeamRelation | null>(null)
const assigning = ref(false)
const unassigning = ref(false)

// 表单数据
const form = reactive({
  teamId: undefined as number | undefined,
  reason: ''
})

// 表单验证规则
const rules: FormRules = {
  teamId: [{ required: true, message: '请选择团队', trigger: 'change' }]
}

// 计算属性
const isBatchMode = computed(() => {
  return props.bizIds && props.bizIds.length > 1
})

const batchItems = computed(() => {
  if (!props.bizIds || !props.bizNames) return [];
  return props.bizIds.map((id, index) => ({
    id,
    name: (props.bizNames && props.bizNames[index]) || `对象${id}`
  }));
});

// 方法
const loadTeams = async () => {
  try {
    // 这里应该调用获取团队列表的API
    // const response = await getTeamList()
    // availableTeams.value = response.data || []
    
    // 临时模拟数据
    availableTeams.value = [
      { id: 1, teamName: '销售团队', leaderName: '张三' },
      { id: 2, teamName: '客服团队', leaderName: '李四' },
      { id: 3, teamName: '技术团队', leaderName: '王五' }
    ]
  } catch (error) {
    console.error('加载团队列表失败:', error)
  }
}

const loadCurrentAssignment = async () => {
  if (!props.bizId || !props.bizType || isBatchMode.value) {
    currentTeam.value = null
    return
  }

  try {
    const response = await getTeamByBiz(props.bizId, props.bizType)
    currentTeam.value = response.data || null
    
    // 如果已有分配，设置表单默认值
    if (currentTeam.value) {
      form.teamId = currentTeam.value.teamId
    }
  } catch (error) {
    console.error('加载当前分配失败:', error)
    currentTeam.value = null
  }
}

const handleAssign = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    assigning.value = true
    try {
      if (isBatchMode.value && props.bizIds && props.bizType) {
        // 批量分配
        await batchAssignTeamToBiz(form.teamId!, props.bizIds, props.bizType)
        ElMessage.success(`成功分配 ${props.bizIds.length} 个对象到团队`)
      } else if (props.bizId && props.bizType) {
        // 单个分配
        await assignTeamToBiz(form.teamId!, props.bizId, props.bizType)
        ElMessage.success('分配团队成功')
      }
      
      emit('success')
      handleClose()
    } catch (error) {
      console.error('分配团队失败:', error)
      ElMessage.error('分配团队失败')
    } finally {
      assigning.value = false
    }
  })
}

const handleUnassign = async () => {
  if (!props.bizId || !props.bizType) return
  
  try {
    await ElMessageBox.confirm(
      '确定要取消当前的团队分配吗？',
      '确认取消分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    unassigning.value = true
    await unassignTeamFromBiz(props.bizId, props.bizType)
    ElMessage.success('取消分配成功')
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消分配失败:', error)
      ElMessage.error('取消分配失败')
    }
  } finally {
    unassigning.value = false
  }
}

const handleConfirm = () => {
  handleAssign()
}

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
  resetForm()
}

const resetForm = () => {
  form.teamId = undefined
  form.reason = ''
  formRef.value?.resetFields()
}

const removeBatchItem = (id: number) => {
  // 这里可以实现移除批量选择中的某个项目
  // 需要父组件支持
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听属性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadTeams()
    loadCurrentAssignment()
  }
})

watch(() => [props.bizId, props.bizType], () => {
  if (props.visible) {
    loadCurrentAssignment()
  }
})
</script>

<style scoped>
.team-assign-dialog {
  padding: 0;
}

.current-assignment {
  margin-bottom: 20px;
}

.current-team-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.team-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.team-name {
  font-weight: 500;
}

.team-leader {
  color: #909399;
  font-size: 12px;
}

.batch-items {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.batch-count {
  margin: 8px 0 0 0;
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>
