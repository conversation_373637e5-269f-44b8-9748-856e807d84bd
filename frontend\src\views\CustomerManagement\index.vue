<template>
    <el-container class="customer-management">
        <!-- 使用新的导航组件 -->
        <side-nav
            v-model="activeTab"
            :title="navConfig.title"
            :menu-items="navConfig.menuItems"
        />

        <!-- 主内容区域 -->
        <el-container class="main-container">
            <el-header class="header">
                <h1>客户管理</h1>
                <div class="header-actions">
                    <el-button 
                        type="primary" 
                        size="small"
                        @click="openCustomerDialog"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建客户
                    </el-button>
                </div>
            </el-header>

            <el-main>
                <!-- 使用统一的筛选组件 -->
                <common-filter
                    v-model:searchValue="searchInput"
                    v-model:filterValue="filterType"
                    :config="filterConfig"
                    @search="handleSearch"
                    @filter="handleFilterChange"
                />

                <!-- 数据表格 -->
                <el-table ref="customerTable" :data="customers" border sortable tooltip-effect="dark"
                    :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                    style="width: 100%; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
                    @selection-change="handleSelectionChange">
                    <template v-for="col in tableColumns" :key="col.prop">
                        <el-table-column v-bind="col">
                            <template #default="scope" v-if="col.prop === 'name'">
                                <el-button link type="primary" class="link-button" @click="openDrawer(scope.row)">{{
                                    scope.row.name }}</el-button>
                            </template>
                        </el-table-column>
                    </template>
                    <table-operations :buttons="tableButtons" />
                </el-table>
                
                <!-- 分页组件 -->
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="handlePagination" />
            </el-main>
        </el-container>

        <!-- 抽屉组件 -->
        <el-drawer v-model="drawerVisible" title="客户详情" size="800px">
            <div>客户详情内容</div>
        </el-drawer>

        <!-- 新建客户对话框 -->
        <el-dialog v-model="dialogVisible" title="新建客户" width="600px">
            <el-form :model="newCustomer" label-width="100px">
                <el-form-item label="客户名称">
                    <el-input v-model="newCustomer.name" placeholder="请输入客户名称" />
                </el-form-item>
                <el-form-item label="联系电话">
                    <el-input v-model="newCustomer.phone" placeholder="请输入联系电话" />
                </el-form-item>
                <el-form-item label="电子邮箱">
                    <el-input v-model="newCustomer.email" placeholder="请输入电子邮箱" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelCustomer">取消</el-button>
                <el-button type="primary" @click="saveCustomer">保存</el-button>
            </template>
        </el-dialog>
    </el-container>
</template>

<script setup lang="ts">
import CommonFilter from '@/components/CommonFilter/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import SideNav from '@/components/SideNav/index.vue';
import type { TableButton } from '@/components/TableOperations/index.vue';
import TableOperations from '@/components/TableOperations/index.vue';
import { FilterType } from '@/types';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, watch } from 'vue';
import { navConfig, tableColumns } from './config';

interface CustomerData {
    id: number;
    name: string;
    phone: string;
    email: string;
    industry?: string;
    level?: string;
    delFlag?: string;
}

// 筛选配置
const filterConfig = {
    search: {
        placeholder: '客户名称/手机/电话',
        width: '240px',
        icon: 'Search'
    },
    filter: {
        label: '显示：',
        options: [
            { label: '全部客户', value: 'all' },
            { label: '我负责的', value: 'mine' },
            { label: '下属负责的', value: 'subordinate' },
            { label: '我关注的客户', value: 'following' }
        ],
        buttonStyle: true,
        size: 'default' as const
    }
};

// 查询参数状态定义
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    searchKeyword: '',
    filterType: 'all' as FilterType
});

// 组件状态定义
const activeTab = ref('customers');
const filterType = ref<FilterType>('all');
const searchInput = ref('');
const total = ref(10);
const customers = ref<CustomerData[]>([
    {
        id: 1,
        name: '武汉普锐特科技',
        phone: '18727192911',
        email: 'Neptuneread...',
        industry: 'IT',
        level: 'B (普通客户)',
        delFlag: '0'
    }
]);

const dialogVisible = ref(false);
const newCustomer = ref<CustomerData>({
    id: 0,
    name: '',
    phone: '',
    email: '',
    delFlag: '0'
});

const drawerVisible = ref(false);

// 表格操作按钮配置
const tableButtons: TableButton[] = [
    {
        label: '编辑',
        type: 'primary',
        link: true,
        icon: 'Edit',
        handler: (row: CustomerData) => openDrawer(row)
    },
    {
        label: '分配',
        type: 'info',
        link: true,
        icon: 'Share',
        handler: (row: CustomerData) => handleAssignTeam(row)
    },
    {
        label: '删除',
        type: 'danger',
        link: true,
        icon: 'Delete',
        handler: (row: CustomerData) => handleDelete(row)
    }
];

// 监听筛选类型变化
watch(filterType, (newType: FilterType) => {
    queryParams.filterType = newType;
    handleFilterChange(newType);
});

// 监听搜索输入值变化
watch(searchInput, (newValue: string) => {
    queryParams.searchKeyword = newValue;
    handleSearch(newValue);
});

// 方法定义
const handleSelectionChange = (val: CustomerData[]): void => {
    console.log(val);
};

const openCustomerDialog = (): void => {
    dialogVisible.value = true;
};

const cancelCustomer = (): void => {
    dialogVisible.value = false;
};

const saveCustomer = (): void => {
    console.log('New Customer:', newCustomer.value);
    customers.value.push({
        ...newCustomer.value,
        id: customers.value.length + 1,
    });
    dialogVisible.value = false;
    newCustomer.value = {
        id: 0,
        name: '',
        phone: '',
        email: '',
        delFlag: '0'
    };
    ElMessage.success('新建客户成功');
};

const openDrawer = (row: CustomerData): void => {
    drawerVisible.value = true;
};

const handleDelete = async (row: CustomerData): Promise<void> => {
    try {
        await ElMessageBox.confirm('确认删除该客户吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        const index = customers.value.findIndex(item => item.id === row.id);
        if (index !== -1) {
            customers.value.splice(index, 1);
            ElMessage.success('删除成功');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除客户失败:', error);
            ElMessage.error('删除客户失败');
        }
    }
};

const handleSearch = (value: string): void => {
    queryParams.searchKeyword = value;
    console.log('搜索:', value);
};

const handleFilterChange = (value: FilterType): void => {
    queryParams.filterType = value;
    console.log('筛选:', value);
};

const handlePagination = (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page;
    queryParams.pageSize = val.limit;
    console.log('分页:', val);
};

/**
 * 处理团队分配
 */
const handleAssignTeam = (row: CustomerData): void => {
    console.log('分配团队给客户:', row);
    ElMessage.info('客户团队分配功能开发中...');
};
</script>

<style scoped>
/* 表格样式 */
.el-table {
    margin-bottom: 20px;
}

/* 表格行高度 */
.el-table .el-table__body-wrapper tbody tr {
    height: 20px;
}

.el-table .el-table__body-wrapper tbody td {
    padding: 5px 0;
}

/* 客户管理容器样式 */
.customer-management {
    background-color: #fff;
    height: 100vh;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

/* 主容器样式 */
.main-container {
    flex: 1;
    padding: 0 20px;
    background-color: #fff;
}

/* 头部操作区域样式 */
.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

/* 链接按钮样式 */
.link-button {
    padding: 0;
    height: auto;
    font-weight: normal;

    &:hover {
        text-decoration: underline;
    }
}
</style>