// CRM团队相关类型定义

// CRM团队实体
export interface CrmTeam {
  teamId?: number;
  teamName: string;
  leaderId?: number;
  leaderName?: string;
  description?: string;
  delFlag?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
}

// CRM团队成员实体
export interface CrmTeamMember {
  id?: number;
  teamId: number;
  teamName?: string;
  userId: number;
  userName?: string;
  nickName?: string;
  roleType: 'owner' | 'admin' | 'member';
  joinTime?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
}

// 团队成员角色类型
export type TeamRoleType = 'owner' | 'admin' | 'member';

// 用户选项接口
export interface UserOption {
  userId: number;
  userName: string;
  nickName: string;
  deptName?: string;
  disabled?: boolean;
}

// 团队管理组件属性接口
export interface TeamManagementProps {
  teamId: number;
  visible: boolean;
  readonly?: boolean;
}