package com.ruoyi.crm.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.common.mapper.CrmContactsMapper;
import com.ruoyi.common.mapper.CrmCustomerMapper;
import com.ruoyi.common.mapper.CrmOpportunityMapper;
import com.ruoyi.common.mapper.CrmTeamRelationMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.crm.service.ICrmTeamRelationService;

/**
 * 通用团队业务关联Service业务层处理
 * 
 * 根据深化团队管理实施方案-V2，实现通用的团队与业务模块关联
 * 支持联系人、线索、客户、商机等多种业务类型与团队的关联
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@Service
public class CrmTeamRelationServiceImpl implements ICrmTeamRelationService 
{
    @Autowired
    private CrmTeamRelationMapper crmTeamRelationMapper;

    @Autowired
    private CrmContactsMapper contactsMapper;

    @Autowired
    private CrmCustomerMapper customerMapper;

    @Autowired
    private CrmOpportunityMapper opportunityMapper;

    /**
     * 查询通用团队业务关联
     * 
     * @param id 通用团队业务关联主键
     * @return 通用团队业务关联
     */
    @Override
    public CrmTeamRelation selectCrmTeamRelationById(Long id)
    {
        return crmTeamRelationMapper.selectCrmTeamRelationById(id);
    }

    /**
     * 查询通用团队业务关联列表
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 通用团队业务关联
     */
    @Override
    public List<CrmTeamRelation> selectCrmTeamRelationList(CrmTeamRelation crmTeamRelation)
    {
        List<CrmTeamRelation> relations = crmTeamRelationMapper.selectCrmTeamRelationList(crmTeamRelation);
        return fillBizNames(relations);
    }

    /**
     * 新增通用团队业务关联
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 结果
     */
    @Override
    public int insertCrmTeamRelation(CrmTeamRelation crmTeamRelation)
    {
        crmTeamRelation.setCreateBy(SecurityUtils.getUsername());
        return crmTeamRelationMapper.insertCrmTeamRelation(crmTeamRelation);
    }

    /**
     * 修改通用团队业务关联
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 结果
     */
    @Override
    public int updateCrmTeamRelation(CrmTeamRelation crmTeamRelation)
    {
        crmTeamRelation.setUpdateBy(SecurityUtils.getUsername());
        return crmTeamRelationMapper.updateCrmTeamRelation(crmTeamRelation);
    }

    /**
     * 批量删除通用团队业务关联
     * 
     * @param ids 需要删除的通用团队业务关联主键
     * @return 结果
     */
    @Override
    public int deleteCrmTeamRelationByIds(Long[] ids)
    {
        return crmTeamRelationMapper.deleteCrmTeamRelationByIds(ids);
    }

    /**
     * 删除通用团队业务关联信息
     * 
     * @param id 通用团队业务关联主键
     * @return 结果
     */
    @Override
    public int deleteCrmTeamRelationById(Long id)
    {
        return crmTeamRelationMapper.deleteCrmTeamRelationById(id);
    }

    // ========== 核心业务方法 ==========

    /**
     * 将业务对象分配给团队
     * 
     * @param teamId 团队ID
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 结果
     */
    @Override
    @Transactional
    public int assignTeamToBiz(Long teamId, Long bizId, String bizType)
    {
        // 验证参数
        if (!validateTeamExists(teamId)) {
            throw new RuntimeException("团队不存在: " + teamId);
        }
        if (!validateBizExists(bizId, bizType)) {
            throw new RuntimeException("业务对象不存在: " + bizType + ":" + bizId);
        }
        if (!validateBizType(bizType)) {
            throw new RuntimeException("不支持的业务类型: " + bizType);
        }

        String createBy = SecurityUtils.getUsername();
        return crmTeamRelationMapper.assignBizToTeam(teamId, bizId, bizType, createBy);
    }

    /**
     * 取消业务对象的团队分配
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 结果
     */
    @Override
    @Transactional
    public int unassignTeamFromBiz(Long bizId, String bizType)
    {
        if (!validateBizType(bizType)) {
            throw new RuntimeException("不支持的业务类型: " + bizType);
        }
        return crmTeamRelationMapper.unassignBizFromTeam(bizId, bizType);
    }

    /**
     * 根据业务对象查询所属团队ID
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 团队ID，如果未分配则返回null
     */
    @Override
    public Long getTeamIdByBiz(Long bizId, String bizType)
    {
        if (!validateBizType(bizType)) {
            return null;
        }
        return crmTeamRelationMapper.selectTeamIdByBiz(bizId, bizType);
    }

    /**
     * 根据业务对象查询团队关联信息
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 团队关联信息
     */
    @Override
    public CrmTeamRelation getTeamRelationByBiz(Long bizId, String bizType)
    {
        if (!validateBizType(bizType)) {
            return null;
        }
        CrmTeamRelation relation = crmTeamRelationMapper.selectTeamRelationByBiz(bizId, bizType);
        if (relation != null) {
            relation.setBizName(getBizName(bizId, bizType));
        }
        return relation;
    }

    /**
     * 根据团队ID查询关联的业务对象列表
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选，为空则查询所有类型）
     * @return 业务关联列表
     */
    @Override
    public List<CrmTeamRelation> getBizListByTeam(Long teamId, String bizType)
    {
        if (bizType != null && !validateBizType(bizType)) {
            return new ArrayList<>();
        }
        List<CrmTeamRelation> relations = crmTeamRelationMapper.selectBizListByTeam(teamId, bizType);
        return fillBizNames(relations);
    }

    /**
     * 批量分配业务对象到团队
     * 
     * @param teamId 团队ID
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAssignTeamToBiz(Long teamId, List<Long> bizIds, String bizType)
    {
        if (!validateTeamExists(teamId)) {
            throw new RuntimeException("团队不存在: " + teamId);
        }
        if (!validateBizType(bizType)) {
            throw new RuntimeException("不支持的业务类型: " + bizType);
        }
        if (bizIds == null || bizIds.isEmpty()) {
            return 0;
        }

        String createBy = SecurityUtils.getUsername();
        return crmTeamRelationMapper.batchAssignBizToTeam(teamId, bizIds, bizType, createBy);
    }

    /**
     * 批量取消业务对象的团队分配
     * 
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUnassignTeamFromBiz(List<Long> bizIds, String bizType)
    {
        if (!validateBizType(bizType)) {
            throw new RuntimeException("不支持的业务类型: " + bizType);
        }
        if (bizIds == null || bizIds.isEmpty()) {
            return 0;
        }
        return crmTeamRelationMapper.batchUnassignBizFromTeam(bizIds, bizType);
    }

    /**
     * 检查业务对象是否已分配给团队
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 是否已分配
     */
    @Override
    public boolean isBizAssigned(Long bizId, String bizType)
    {
        if (!validateBizType(bizType)) {
            return false;
        }
        return crmTeamRelationMapper.checkBizAssigned(bizId, bizType) > 0;
    }

    /**
     * 统计团队关联的业务对象数量
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选）
     * @return 关联数量
     */
    @Override
    public int countBizByTeam(Long teamId, String bizType)
    {
        if (bizType != null && !validateBizType(bizType)) {
            return 0;
        }
        return crmTeamRelationMapper.countBizByTeam(teamId, bizType);
    }

    /**
     * 根据业务类型统计各团队的业务对象数量
     *
     * @param bizType 业务类型
     * @return 统计结果列表
     */
    @Override
    public List<CrmTeamRelation> countBizByTeamAndType(String bizType)
    {
        if (bizType != null && !validateBizType(bizType)) {
            return new ArrayList<>();
        }
        return crmTeamRelationMapper.countBizByTeamAndType(bizType);
    }

    // ========== 业务验证方法 ==========

    /**
     * 验证团队是否存在
     *
     * @param teamId 团队ID
     * @return 是否存在
     */
    @Override
    public boolean validateTeamExists(Long teamId)
    {
        if (teamId == null) {
            return false;
        }
        // TODO: 实现团队存在性验证
        // 可以通过查询crm_teams表来验证
        return true; // 临时返回true，待实现
    }

    /**
     * 验证业务对象是否存在
     *
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 是否存在
     */
    @Override
    public boolean validateBizExists(Long bizId, String bizType)
    {
        if (bizId == null || StringUtils.isEmpty(bizType)) {
            return false;
        }

        try {
            switch (bizType) {
                case CrmTeamRelation.BizType.CONTACT:
                    return contactsMapper.selectCrmContactsById(bizId) != null;
                case CrmTeamRelation.BizType.CUSTOMER:
                    return customerMapper.selectCrmCustomerById(bizId) != null;
                case CrmTeamRelation.BizType.OPPORTUNITY:
                    return opportunityMapper.selectCrmOpportunityById(bizId) != null;
                case CrmTeamRelation.BizType.LEAD:
                case CrmTeamRelation.BizType.CONTRACT:
                case CrmTeamRelation.BizType.VISIT_PLAN:
                    // TODO: 实现其他业务类型的验证
                    return true; // 临时返回true
                default:
                    return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证业务类型是否支持
     *
     * @param bizType 业务类型
     * @return 是否支持
     */
    @Override
    public boolean validateBizType(String bizType)
    {
        if (StringUtils.isEmpty(bizType)) {
            return false;
        }

        return CrmTeamRelation.BizType.CONTACT.equals(bizType) ||
               CrmTeamRelation.BizType.LEAD.equals(bizType) ||
               CrmTeamRelation.BizType.CUSTOMER.equals(bizType) ||
               CrmTeamRelation.BizType.OPPORTUNITY.equals(bizType) ||
               CrmTeamRelation.BizType.CONTRACT.equals(bizType) ||
               CrmTeamRelation.BizType.VISIT_PLAN.equals(bizType);
    }

    // ========== 业务名称获取方法 ==========

    /**
     * 根据业务对象获取业务名称
     *
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 业务名称
     */
    @Override
    public String getBizName(Long bizId, String bizType)
    {
        if (bizId == null || StringUtils.isEmpty(bizType)) {
            return "";
        }

        try {
            switch (bizType) {
                case CrmTeamRelation.BizType.CONTACT:
                    var contact = contactsMapper.selectCrmContactsById(bizId);
                    return contact != null ? contact.getName() : "";
                case CrmTeamRelation.BizType.CUSTOMER:
                    var customer = customerMapper.selectCrmCustomerById(bizId);
                    return customer != null ? customer.getCustomerName() : "";
                case CrmTeamRelation.BizType.OPPORTUNITY:
                    var opportunity = opportunityMapper.selectCrmOpportunityById(bizId);
                    return opportunity != null ? opportunity.getOpportunityName() : "";
                case CrmTeamRelation.BizType.LEAD:
                case CrmTeamRelation.BizType.CONTRACT:
                case CrmTeamRelation.BizType.VISIT_PLAN:
                    // TODO: 实现其他业务类型的名称获取
                    return bizType + ":" + bizId;
                default:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 批量获取业务名称
     *
     * @param relations 团队关联列表
     * @return 填充了业务名称的关联列表
     */
    @Override
    public List<CrmTeamRelation> fillBizNames(List<CrmTeamRelation> relations)
    {
        if (relations == null || relations.isEmpty()) {
            return relations;
        }

        for (CrmTeamRelation relation : relations) {
            String bizName = getBizName(relation.getBizId(), relation.getBizType());
            relation.setBizName(bizName);
        }

        return relations;
    }
}
