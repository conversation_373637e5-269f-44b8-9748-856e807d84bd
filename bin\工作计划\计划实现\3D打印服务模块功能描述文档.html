<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D打印服务模块功能描述文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #34495e;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        
        h3 {
            color: #2980b9;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 5px;
        }
        
        .module-overview {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .api-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .api-table th, .api-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .api-table th {
            background: #3498db;
            color: white;
            font-weight: bold;
        }
        
        .api-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .flow-diagram {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .sequence-diagram {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .tech-specs {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Consolas', monospace;
            overflow-x: auto;
        }
        
        .mermaid {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 3px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-error { background: #f8d7da; color: #721c24; }
        
        ul, ol {
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        .process-step {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
    </style>
    <!-- 使用本地 Mermaid 库 -->
    <script src="../js/mermaid.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>3D打印服务模块功能描述文档</h1>
        
        <div class="module-overview">
            <h2>📋 模块概述</h2>
            <p>3D打印服务模块是CRM系统中的核心业务模块，为客户提供完整的3D打印定制化服务流程。该模块包含<strong>报价管理</strong>、<strong>模型管理</strong>和<strong>订单导出</strong>三个子模块，支持从文件上传、智能分析、参数选择到最终报价生成的全流程自动化处理。</p>
        </div>

        <h2>🚀 一、功能介绍</h2>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>📊 报价管理 (ThreeDPrintingQuote)</h4>
                <ul>
                    <li>支持多种3D模型文件上传（STL、STEP等）</li>
                    <li>自动分析模型尺寸、体积、表面积</li>
                    <li>智能材料推荐与价格计算</li>
                    <li>可视化参数选择界面</li>
                    <li>实时报价计算与订单生成</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4>🎯 模型管理 (ThreeDModels)</h4>
                <ul>
                    <li>基于Three.js的3D模型预览</li>
                    <li>模型旋转、缩放、平移操作</li>
                    <li>实时体积计算与尺寸显示</li>
                    <li>相机位置跟踪与记录</li>
                    <li>STL格式模型加载与渲染</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4>📄 订单导出 (QuoteExport)</h4>
                <ul>
                    <li>专业报价单PDF生成</li>
                    <li>电子印章集成支持</li>
                    <li>自定义印章上传功能</li>
                    <li>标准化报价单格式</li>
                    <li>一键导出与分享</li>
                </ul>
            </div>
        </div>

        <h2>🔗 二、API接口</h2>
        
        <table class="api-table">
            <thead>
                <tr>
                    <th>接口名称</th>
                    <th>路径</th>
                    <th>方法</th>
                    <th>功能描述</th>
                    <th>主要参数</th>
                    <th>返回值说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>获取打印模型列表</td>
                    <td>/crm/product/list</td>
                    <td>GET</td>
                    <td>分页获取可用的3D打印材料和产品信息</td>
                    <td>pageNum, pageSize, type</td>
                    <td>分页的产品列表数据</td>
                </tr>
                <tr>
                    <td>获取产品类型</td>
                    <td>/crm/product/types</td>
                    <td>GET</td>
                    <td>获取所有可用的产品材料类型</td>
                    <td>无</td>
                    <td>类型字符串数组</td>
                </tr>
                <tr>
                    <td>文件上传</td>
                    <td>临时存储</td>
                    <td>POST</td>
                    <td>上传3D模型文件到服务器临时存储</td>
                    <td>file, maxSize, formats</td>
                    <td>临时文件URL和处理结果</td>
                </tr>
                <tr>
                    <td>订单生成</td>
                    <td>/crm/order/create</td>
                    <td>POST</td>
                    <td>根据报价信息生成正式订单，同时将文件上传至OSS永久存储</td>
                    <td>quoteData, customerInfo</td>
                    <td>订单编号和状态</td>
                </tr>
            </tbody>
        </table>

        <h2>📊 三、业务流程图</h2>
        
        <div class="flow-diagram">
            <h3>3D打印服务完整流程</h3>
            <div class="mermaid">
flowchart TD
    A[客户访问报价页面] --> B[上传3D模型文件]
    B --> C{文件格式检查}
    C -->|格式正确| D[文件临时存储在服务器]
    C -->|格式错误| E[显示错误提示]
    E --> B

    D --> F[自动分析模型信息]
    F --> G[提取尺寸、体积、表面积]
    G --> H[显示模型预览]
    H --> I[选择打印材料]

    I --> J[打开材料选择对话框]
    J --> K[筛选材料类型]
    K --> L[查看材料属性]
    L --> M[选择具体材料规格]
    M --> N[配置后处理选项]

    N --> O[计算价格]
    O --> P[显示报价详情]
    P --> Q["客户确认"]
    Q -->|确认| R[生成订单]
    Q -->|修改| I
    Q -->|取消| S[返回首页]

    R --> T[订单导出页面]
    T --> U[选择印章]
    U --> V[生成PDF报价单]
    V --> W[下载/分享报价单]

    H --> X[3D模型预览]
    X --> Y[模型旋转/缩放]
    Y --> Z[查看模型详情]
    Z --> I
            </div>
        </div>

        <h2>🏗️ 四、系统架构图</h2>
        
        <div class="flow-diagram">
            <h3>模块架构关系</h3>
            <div class="mermaid">
graph TD
    A[3D打印服务模块] --> B[报价管理]
    A --> C[模型管理]
    A --> D[订单导出]

    B --> E[文件上传组件]
    B --> F[报价表格组件]
    B --> G[参数选择对话框]

    C --> H[Three.js渲染引擎]
    C --> I[STL加载器]
    C --> J[轨道控制器]

    D --> K[PDF生成器]
    D --> L[印章管理]
    D --> M[模板渲染]

    E --> N[临时文件存储]
    E --> O[文件处理器]
    F --> P[产品API]
    G --> Q[材料类型API]

    D --> R[OSS文件存储]

    K --> S[html2canvas]
    K --> T[jsPDF]

    subgraph 后端服务
        P --> U[CRM产品控制器]
        Q --> V[材料管理服务]
        W[订单管理服务]
    end

    subgraph 前端组件
        E
        F
        G
        H
        I
        J
        K
        L
        M
    end

    subgraph 外部服务
        N
        R
        S
    end
            </div>
        </div>

        <h2>💾 五、数据实体图</h2>
        
        <div class="flow-diagram">
            <h3>核心数据结构</h3>
            <div class="mermaid">
erDiagram
    QuoteOrder {
        string id PK
        string customerId FK
        string fileName
        decimal volume
        decimal surfaceArea
        string materialId FK
        string processOptions
        decimal totalPrice
        date createTime
        string status
    }
    
    PrintingMaterial {
        string id PK
        string name
        string type
        decimal pricePerCubicCm
        string color
        string properties
        boolean isActive
    }
    
    ModelFile {
        string id PK
        string quoteId FK
        string fileUrl
        string fileFormat
        decimal sizeInMB
        date uploadTime
        string status
    }
    
    PostProcess {
        string id PK
        string name
        string description
        decimal additionalCost
        boolean needsManualQuote
    }
    
    QuoteOrder ||--o{ ModelFile : "contains"
    QuoteOrder ||--|| PrintingMaterial : "uses"
    QuoteOrder ||--o{ PostProcess : "applies"
            </div>
        </div>

        <h2>📱 六、界面原型图</h2>
        
        <!-- 报价流程时序图 -->
        <div class="sequence-diagram">
            <h3>报价流程时序图</h3>
            <div class="mermaid">
sequenceDiagram
    actor C as 客户
    participant P as 报价页面
    participant U as 文件上传组件
    participant V as 3D预览组件
    participant S as 参数选择组件
    participant Q as 报价计算服务
    participant O as OSS存储服务
    participant D as PDF生成服务
    
    C->>P: 访问3D打印报价页面
    P->>C: 显示文件上传界面
    C->>U: 上传STL/STEP文件
    activate U
    U->>U: 验证文件格式和大小
    U-->>P: 上传成功(临时存储在服务器)
    deactivate U
    P->>V: 传入模型临时文件URL
    activate V
    V->>V: 加载并渲染3D模型
    V->>V: 计算体积和表面积
    V-->>P: 返回模型分析数据
    deactivate V
    P->>S: 打开参数配置界面
    activate S
    S->>C: 显示材料和工艺选项
    C->>S: 选择材料、工艺、数量
    S->>Q: 提交参数计算报价
    activate Q
    Q-->>S: 返回详细报价结果
    deactivate Q
    S->>C: 显示报价详情
    deactivate S
    C->>P: 确认报价并提交订单
    
    P->>O: 上传文件到OSS存储
    activate O
    O-->>P: 返回OSS存储URL
    deactivate O
    P->>D: 提交报价单生成请求
    activate D
    D-->>P: 返回PDF文件URL
    deactivate D
    P->>C: 提供报价单下载链接
            </div>
        </div>

        <!-- 3D模型预览时序图 -->
        <div class="sequence-diagram">
            <h3>3D模型预览时序图</h3>
            <div class="mermaid">
sequenceDiagram
    actor C as 客户
    participant P as 产品页面
    participant M as 模型预览组件
    participant L as Three.js加载器
    participant R as 渲染引擎
    participant A as 分析服务
    
    C->>P: 点击"预览3D模型"
    activate P
    P->>M: 初始化预览组件
    activate M
    M->>L: 请求加载STL模型
    activate L
    L->>L: 解析STL文件几何数据
    L-->>M: 返回模型几何数据
    deactivate L
    M->>R: 传递几何数据进行渲染
    activate R
    R->>R: 设置材质和光照
    R->>R: 渲染模型到Canvas
    R-->>M: 渲染完成
    deactivate R
    M->>A: 请求模型体积计算
    activate A
    A->>A: 执行三角网格体积计算
    A-->>M: 返回体积数据
    deactivate A
    M-->>P: 更新UI显示
    deactivate M
    P->>C: 显示交互式3D模型
    C->>M: 旋转/缩放操作
    activate M
    M->>R: 更新相机位置
    activate R
    R->>R: 重新渲染视图
    R-->>M: 渲染完成
    deactivate R
    M-->>C: 显示新视角
    deactivate M
    deactivate P
            </div>
        </div>

        <h2>🛠️ 七、技术实现细节</h2>
        
        <div class="tech-specs">
            <h3>关键技术细节</h3>
            <h3>支持的文件格式</h3>
            <ul>
                <li><strong>STL格式</strong>：标准三角网格格式，支持二进制和ASCII编码</li>
                <li><strong>STEP格式</strong>：工业标准参数化CAD格式（.step, .stp）</li>
                <li><strong>压缩包</strong>：支持ZIP和RAR格式的批量文件上传</li>
            </ul>
            
            <h3>文件限制</h3>
            <ul>
                <li>单个文件最大大小：100MB</li>
                <li>单次上传文件数量：最多20个</li>
                <li>支持的单位：毫米（mm）、英寸（inch）</li>
            </ul>
            
            <h3>3D渲染技术</h3>
            <ul>
                <li><strong>Three.js</strong>：WebGL 3D图形库</li>
                <li><strong>STL加载器</strong>：自动解析STL文件几何数据</li>
                <li><strong>轨道控制器</strong>：支持鼠标交互操作</li>
                <li><strong>体积计算</strong>：基于三角网格的精确体积计算</li>
            </ul>
            
            <h3>PDF生成技术</h3>
            <ul>
                <li><strong>html2canvas</strong>：HTML到图像转换</li>
                <li><strong>jsPDF</strong>：客户端PDF生成</li>
                <li><strong>印章支持</strong>：SVG格式电子印章</li>
            </ul>
        </div>

        <h2>⚠️ 八、业务规则说明</h2>
        
        <div class="warning">
            <h3>⚠️ 重要业务规则</h3>
            <ul>
                <li><strong>合规性要求</strong>：严禁利用平台生产违禁产品（管制刀具、枪支弹药等）</li>
                <li><strong>文件保密</strong>：所有上传文件严格保密，保护客户知识产权</li>
                <li><strong>价格计算</strong>：基于材料成本、加工难度、后处理工艺综合定价</li>
                <li><strong>交期规则</strong>：工作日计算，节假日自动顺延</li>
                <li><strong>起步价</strong>：订单金额不足起步价时按起步价结算</li>
            </ul>
        </div>

        <h2>🚀 九、业务流程步骤</h2>
        
        <div class="process-step">
            <div class="step-number">1</div>
            <div>
                <strong>文件上传阶段</strong>
                <p>用户选择3D模型文件，系统验证格式和大小，临时存储在服务器</p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">2</div>
            <div>
                <strong>模型分析阶段</strong>
                <p>自动解析模型文件，提取尺寸、体积、表面积等关键信息</p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">3</div>
            <div>
                <strong>材料选择阶段</strong>
                <p>根据模型特点推荐合适材料，用户可自主选择材料类型和规格</p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">4</div>
            <div>
                <strong>参数配置阶段</strong>
                <p>设置打印参数、后处理选项（喷漆、丝印、镶嵌等）</p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">5</div>
            <div>
                <strong>报价生成阶段</strong>
                <p>根据选择的材料和参数自动计算价格，生成详细报价单</p>
            </div>
        </div>
        
        <div class="process-step">
            <div class="step-number">6</div>
            <div>
                <strong>订单确认阶段</strong>
                <p>用户确认报价后生成正式订单，此时文件上传至OSS存储进行永久保存，支持PDF导出和印章签署</p>
            </div>
        </div>

        <h2>📈 十、系统特色功能</h2>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🎯 智能分析</h4>
                <p>自动分析3D模型的几何特征，精确计算体积、表面积等参数，为报价提供科学依据</p>
            </div>
            
            <div class="feature-card">
                <h4>🔄 实时预览</h4>
                <p>基于WebGL技术的3D模型实时预览，支持360度旋转、缩放操作，让用户直观了解模型</p>
            </div>
            
            <div class="feature-card">
                <h4>💰 动态报价</h4>
                <p>根据材料选择、数量变化实时更新价格，提供透明的成本计算过程</p>
            </div>
            
            <div class="feature-card">
                <h4>📄 专业文档</h4>
                <p>自动生成标准化报价单，支持电子印章，提供专业的商务文档服务</p>
            </div>
        </div>

        <h2>🎉 十一、总结</h2>
        
        <p>3D打印服务模块通过整合<span class="highlight">文件上传</span>、<span class="highlight">模型分析</span>、<span class="highlight">材料选择</span>、<span class="highlight">参数配置</span>、<span class="highlight">报价生成</span>和<span class="highlight">订单管理</span>等核心功能，为用户提供了一站式的3D打印定制服务体验。</p>
        
        <p>该模块具有以下优势：</p>
        <ul>
            <li>🚀 <strong>高效便捷</strong>：自动化的文件处理和报价生成，大幅提升用户体验</li>
            <li>🎯 <strong>精确专业</strong>：基于模型几何分析的科学报价，确保价格合理透明</li>
            <li>🔧 <strong>灵活定制</strong>：丰富的材料选择和后处理选项，满足不同需求</li>
            <li>📱 <strong>现代化界面</strong>：基于Vue3 + Element Plus的响应式设计，操作简单直观</li>
        </ul>
        
        <p>通过本模块，客户可以快速获得专业的3D打印报价，简化了传统制造业的询价流程，提高了业务效率。</p>
    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                wrap: true
            }
        });
    </script>
</body>
</html>
