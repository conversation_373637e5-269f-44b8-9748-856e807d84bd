<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
import { SwitchButton, User } from '@element-plus/icons-vue';
import { ElButton, ElDialog } from 'element-plus';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { getHeaderMenu } from '~/api/menu';
// TODO: 主题切换功能暂时屏蔽，后续需要重新实现
// import { toggleDark } from "~/composables";
interface MenuItem {
  menuId: number;
  menuName: string;
  parentId: number;
  orderNum: number;
  path: string;
  component: string;
  query: string;
  isFrame: string;
  isCache: string;
  menuType: string;
  visible: string;
  status: string;
  perms: string;
  icon: string;
  children: MenuItem[];
  enabled: string;
}

const defaultMenuItems: MenuItem[] = [
  {
    menuId: 1,
    menuName: '客户管理',
    parentId: 0,
    orderNum: 1,
    path: '/crm/customer',
    component: 'CustomerManagement',
    query: '',
    isFrame: '1',
    isCache: '0',
    menuType: 'C',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'User',
    children: [],
    enabled: '0'
  },
  {
    menuId: 2,
    menuName: '联系人管理',
    parentId: 0,
    orderNum: 2,
    path: '/crm/contact',
    component: 'ContactManagement',
    query: '',
    isFrame: '1',
    isCache: '0',
    menuType: 'C',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'Phone',
    children: [],
    enabled: '0'
  },
  {
    menuId: 3,
    menuName: '关联管理',
    parentId: 0,
    orderNum: 3,
    path: '/crm/association',
    component: 'AssociationManagement',
    query: '',
    isFrame: '1',
    isCache: '0',
    menuType: 'C',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'Connection',
    children: [],
    enabled: '0'
  },
  {
    menuId: 4,
    menuName: '合同管理',
    parentId: 0,
    orderNum: 4,
    path: '/crm/contract',
    component: 'ContractManagement',
    query: '',
    isFrame: '1',
    isCache: '0',
    menuType: 'C',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'Document',
    children: [],
    enabled: '0'
  },
  {
    menuId: 5,
    menuName: '付款管理',
    parentId: 0,
    orderNum: 5,
    path: '/crm/payment',
    component: 'PaymentManagement',
    query: '',
    isFrame: '1',
    isCache: '0',
    menuType: 'C',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'Money',
    children: [],
    enabled: '0'
  },
  {
    menuId: 6,
    menuName: '商机管理',
    parentId: 0,
    orderNum: 6,
    path: '/crm/opportunity',
    component: 'BusinessOpportunity',
    query: '',
    isFrame: '1',
    isCache: '0',
    menuType: 'C',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'Opportunity',
    children: [],
    enabled: '0'
  }
];

const menuItems = ref<MenuItem[]>([]);
const isMenuLoading = ref(true);

const loadMenuItems = async () => {
  try {
    const { data } = await getHeaderMenu();
    console.log('菜单数据加载成功:', data);
    
    // 验证数据完整性
    if (Array.isArray(data)) {
      // 检查是否有重复的menuId
      const menuIds = data.map(item => item.menuId);
      const uniqueMenuIds = [...new Set(menuIds)];
      
      if (menuIds.length !== uniqueMenuIds.length) {
        console.warn('检测到重复的菜单ID:', menuIds);
      }
      
      menuItems.value = data;
      console.log('处理后的可见菜单项:', visibleMenuItems.value);
    } else {
      console.error('菜单数据格式错误:', data);
      menuItems.value = defaultMenuItems;
    }
  } catch (error) {
    console.error('Failed to load menu items:', error);
    // 当API调用失败时使用默认菜单数据
    menuItems.value = defaultMenuItems;
  } finally {
    isMenuLoading.value = false;
  }
};

const router = useRouter();
const userStore = useUserStore();

// 用户信息
const userInfo = computed(() => ({
  name: userStore.name,
  avatar: userStore.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
}));

// 过滤并处理可见的菜单项
const visibleMenuItems = computed(() => {
  if (!menuItems.value || menuItems.value.length === 0) {
    return [];
  }
  
  // 只显示子菜单项（parentId !== 0），过滤掉父菜单
  return menuItems.value
    .filter(item => 
      item &&
      typeof item.menuId === 'number' &&
      item.menuId > 0 &&
      item.parentId !== 0 && 
      item.visible === '0' && 
      item.status === '0' &&
      item.menuType === 'C' && // 只显示菜单类型，不显示目录和按钮
      item.path &&
      item.menuName
    )
    .map((item, index) => ({
      ...item,
      // 确保每个菜单项都有唯一的标识，避免任何可能的重复
      uniqueKey: `menu-${item.menuId}-${index}-${Date.now()}`
    }));
});

// 添加对话框相关的状态
const logoutDialogVisible = ref(false);

// 处理下拉菜单命令
const handleCommand = async (command: string) => {
  if (command === 'logout') {
    // 显示自定义对话框
    logoutDialogVisible.value = true;
  } else if (command === 'profile') {
    router.push('/user/profile');
  }
};

// 处理确认退出
const handleConfirmLogout = async () => {
  try {
    // 关闭对话框
    logoutDialogVisible.value = false;
    // 执行退出登录操作
    await userStore.logout();
    router.push('/login');
  } catch (error) {
    console.error('Failed to logout:', error);
  }
};

// 处理取消退出
const handleCancelLogout = () => {
  logoutDialogVisible.value = false;
};

onMounted(() => {
  loadMenuItems();
});
</script>

<template>
  <div class="header-container">
    <el-menu class="el-menu-demo" mode="horizontal">
      <template v-if="!isMenuLoading && visibleMenuItems.length > 0">
        <template v-for="item in visibleMenuItems" :key="item.uniqueKey">
          <el-menu-item 
            :index="item.path"
            @click="$router.push(item.path)"
          >
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.menuName }}</span>
          </el-menu-item>
        </template>
      </template>
      <!-- TODO: 主题切换功能暂时屏蔽，后续需要重新实现 -->
      <!-- <el-menu-item h="full" @click="toggleDark()" key="dark-mode-toggle">
        <button class="border-none w-full bg-transparent cursor-pointer" style="height: var(--ep-menu-item-height)">
          <i inline-flex i="dark:ep-moon ep-sunny" />
        </button>
      </el-menu-item> -->
    </el-menu>

    <!-- 用户头像和下拉菜单 -->
    <div class="user-menu">
      <el-dropdown trigger="click" @command="handleCommand">
        <span class="user-info">
          <img :src="userInfo.avatar" class="avatar">
          <span class="name">{{ userInfo.name }}</span>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>个人中心
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 自定义退出登录对话框 -->
    <el-dialog
      v-model="logoutDialogVisible"
      title="提示"
      width="30%"
      center
      :close-on-click-modal="false"
      :show-close="true"
    >
      <span>确定注销并退出系统吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelLogout">取消</el-button>
          <el-button type="primary" @click="handleConfirmLogout">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as vars;

.header-container {
  margin:2px;
  padding: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  background: vars.$gradient-background-header;
  // border-radius: 8px;
  // box-shadow: $box-shadow-light;
}

.el-menu-demo {
  padding-left: 16px;
  flex: 1;
  background: transparent;
}

.el-menu-item {
  display: flex;
  align-items: center;
  color: #fff;
}

.el-icon {
  margin-right: 4px;
  color: #fff;
}

.user-menu {
  margin-left: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid #fff;
}

.name {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
}
</style>
