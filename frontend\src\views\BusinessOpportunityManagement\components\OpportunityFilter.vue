<template>
  <el-form
    ref="formRef"
    :model="filterForm"
    :inline="true"
    class="opportunity-filter"
  >
    <el-form-item label="商机名称">
      <el-input
        v-model="filterForm.name"
        placeholder="请输入商机名称"
        clearable
        @keyup.enter="handleSearch"
      />
    </el-form-item>

    <el-form-item label="所属客户">
      <el-select
        v-model="filterForm.customerId"
        placeholder="请选择客户"
        clearable
        filterable
        remote
        :remote-method="handleCustomerSearch"
        :loading="customerLoading"
      >
        <el-option
          v-for="item in customerOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="商机阶段">
      <el-select
        v-model="filterForm.stage"
        placeholder="请选择商机阶段"
        clearable
        multiple
        collapse-tags
        collapse-tags-tooltip
      >
        <el-option
          v-for="item in stageOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <el-tag :type="getStageTagType(item.value)" size="small">
            {{ item.label }}
          </el-tag>
        </el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="预计金额">
      <el-input-number
        v-model="filterForm.minAmount"
        :precision="2"
        :step="1000"
        :min="0"
        controls-position="right"
        placeholder="最小金额"
        style="width: 150px"
      />
      <span class="separator">-</span>
      <el-input-number
        v-model="filterForm.maxAmount"
        :precision="2"
        :step="1000"
        :min="0"
        controls-position="right"
        placeholder="最大金额"
        style="width: 150px"
      />
    </el-form-item>

    <el-form-item label="成交概率">
      <el-select
        v-model="filterForm.probabilityRange"
        placeholder="请选择概率范围"
        clearable
      >
        <el-option label="0-20%" value="0-20" />
        <el-option label="21-40%" value="21-40" />
        <el-option label="41-60%" value="41-60" />
        <el-option label="61-80%" value="61-80" />
        <el-option label="81-100%" value="81-100" />
      </el-select>
    </el-form-item>

    <el-form-item label="预计成交日期">
      <el-date-picker
        v-model="filterForm.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { searchCustomers } from '@/api/customer'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { defineEmits, reactive, ref } from 'vue'

const emit = defineEmits(['search', 'reset'])

const formRef = ref<FormInstance>()
const customerLoading = ref(false)
const customerOptions = ref([])

const stageOptions = [
  { label: '初步接触', value: 'initial_contact' },
  { label: '需求分析', value: 'needs_analysis' },
  { label: '方案/报价', value: 'proposal' },
  { label: '谈判/复审', value: 'negotiation' },
  { label: '赢单', value: 'closed_won' },
  { label: '输单', value: 'closed_lost' }
]

const filterForm = reactive({
  name: '',
  customerId: '',
  stage: [],
  minAmount: null,
  maxAmount: null,
  probabilityRange: '',
  dateRange: []
})

const getStageTagType = (stage: string) => {
  const stageMap: Record<string, string> = {
    'initial_contact': 'info',
    'needs_analysis': 'warning',
    'proposal': 'success',
    'negotiation': 'danger',
    'closed_won': 'success',
    'closed_lost': 'info'
  }
  return stageMap[stage] || 'info'
}

const handleCustomerSearch = async (query: string) => {
  if (query) {
    customerLoading.value = true
    try {
      const res = await searchCustomers({ keyword: query })
      customerOptions.value = res.data
    } catch (error) {
      ElMessage.error('获取客户列表失败')
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

const handleSearch = () => {
  const [startDate, endDate] = filterForm.dateRange || []
  const [minProbability, maxProbability] = filterForm.probabilityRange
    ? filterForm.probabilityRange.split('-').map(Number)
    : []

  const searchParams = {
    name: filterForm.name,
    customerId: filterForm.customerId,
    stage: filterForm.stage,
    minAmount: filterForm.minAmount,
    maxAmount: filterForm.maxAmount,
    minProbability,
    maxProbability,
    startDate,
    endDate
  }

  emit('search', searchParams)
}

const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  emit('reset')
}
</script>

<style scoped>
.opportunity-filter {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.separator {
  margin: 0 8px;
  color: #909399;
}
</style> 