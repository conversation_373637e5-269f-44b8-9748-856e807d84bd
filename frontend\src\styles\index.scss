// TODO: 主题切换功能暂时屏蔽，后续需要重新实现
// import dark theme
// @use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;

// 使用新的@use语法导入所有变量
@use './variables/colors' as *;
@use './variables/typography' as *;
@use './variables/spacing' as *;
@use './variables/buttons' as *;
@use './variables/radio-button' as *;
@use './variables' as *;

// 如果有其他样式文件也需要更新
@use './components/radio-button' as *;

// 强制修复 radio-button 样式
@import './radio-button-fix.scss';

// 如果需要转发变量给其他文件使用，可以使用@forward
@forward './variables/colors';
@forward './variables/typography';
@forward './variables/spacing';
@forward './variables/buttons';
@forward './variables/radio-button';
@forward './variables';

// TODO: 主题切换功能暂时屏蔽，后续需要重新实现
// 导入主题
// @use './themes/dark' as *;

// 注意：element-override.scss 现在在 main.ts 中导入

:root {
    --color-primary: #0066ff;
   /* 主色，稍高饱和度 */
   --ep-color-primary: #0066ff;
  // --ep-color-primary: -webkit-linear-gradient(to right, #0066ff, #668cff);
  // --ep-color-primary: linear-gradient(to right, #0066ff, #668cff);
  /* 其他变量保持不变 */
   /* 白色 */
   --ep-color-white: #ffffff;
   /* 主色的较亮变种，用于按钮背景初始状态 */
   --ep-color-primary-light-9: #e0eaff;
   /* 主色的适中亮度变种，用于按钮边框初始状态 */
   --ep-color-primary-light-5: #99b3ff;

   /* 按钮文字颜色，默认使用主色 */
   --ep-button-text-color: var(--ep-color-primary);
   /* 按钮背景颜色，使用主色的较亮变种 */
   --ep-button-bg-color: var(--ep-color-primary-light-9);
   /* 按钮边框颜色，使用主色的适中亮度变种 */
   --ep-button-border-color: var(--ep-color-primary-light-5);
   /* 按钮悬停时文字颜色，使用白色 */
   --ep-button-hover-text-color: var(--ep-color-white);
   /* 按钮悬停时背景颜色，使用主色 */
   --ep-button-hover-bg-color: var(--ep-color-primary);
   /* 按钮悬停时边框颜色，使用主色 */
   --ep-button-hover-border-color: var(--ep-color-primary);
   /* 按钮激活时文字颜色，使用白色 */
   --ep-button-active-text-color: var(--ep-color-white);
   /* 按钮激活时背景颜色，使用比主色稍深一点的蓝色，突出激活效果 */
   --ep-button-active-bg-color: #0052cc;
   /* 按钮激活时边框颜色，同样使用比主色稍深一点的蓝色 */
   --ep-button-active-border-color: #0052cc;
   /* 按钮聚焦时文字颜色，使用白色 */
   --ep-button-focus-text-color: var(--ep-color-white);
   /* 按钮聚焦时背景颜色，使用主色 */
   --ep-button-focus-bg-color: var(--ep-color-primary);
   /* 按钮聚焦时边框颜色，使用主色 */
   --ep-button-focus-border-color: var(--ep-color-primary);
   /* 按钮禁用时文字颜色，使用灰色，体现不可用状态 */
   --ep-button-disabled-text-color: #9e9e9e;
   /* 按钮禁用时背景颜色，使用较淡的灰色 */
   --ep-button-disabled-bg-color: #e0e0e0;
   /* 按钮禁用时边框颜色，使用更淡的灰色 */
   --ep-button-disabled-border-color: #bdbdbd;
   --ep-color-primary-light-3: linear-gradient(to right, #007bff, #7ea0ff);

   --ep-font-weight-primary: 200;

}

// :root {
//    /* 主色，多点灰度 */
//    --ep-color-primary: #668cff;
//    /* 白色 */
//    --ep-color-white: #ffffff;
//    /* 主色的较亮变种，用于按钮背景初始状态 */
//    --ep-color-primary-light-9: #e8eaff;
//    /* 主色的适中亮度变种，用于按钮边框初始状态 */
//    --ep-color-primary-light-5: #aab3ff;

//    /* 按钮文字颜色，默认使用主色 */
//    --ep-button-text-color: var(--ep-color-primary);
//    /* 按钮背景颜色，使用主色的较亮变种 */
//    --ep-button-bg-color: var(--ep-color-primary-light-9);
//    /* 按钮边框颜色，使用主色的适中亮度变种 */
//    --ep-button-border-color: var(--ep-color-primary-light-5);
//    /* 按钮悬停时文字颜色，使用白色 */
//    --ep-button-hover-text-color: var(--ep-color-white);
//    /* 按钮悬停时背景颜色，使用主色 */
//    --ep-button-hover-bg-color: var(--ep-color-primary);
//    /* 按钮悬停时边框颜色，使用主色 */
//    --ep-button-hover-border-color: var(--ep-color-primary);
//    /* 按钮激活时文字颜色，使用白色 */
//    --ep-button-active-text-color: var(--ep-color-white);
//    /* 按钮激活时背景颜色，使用比主色稍深一点的颜色，突出激活效果 */
//    --ep-button-active-bg-color: #5273e6;
//    /* 按钮激活时边框颜色，同样使用比主色稍深一点的颜色 */
//    --ep-button-active-border-color: #5273e6;
//    /* 按钮聚焦时文字颜色，使用白色 */
//    --ep-button-focus-text-color: var(--ep-color-white);
//    /* 按钮聚焦时背景颜色，使用主色 */
//    --ep-button-focus-bg-color: var(--ep-color-primary);
//    /* 按钮聚焦时边框颜色，使用主色 */
//    --ep-button-focus-border-color: var(--ep-color-primary);
//    /* 按钮禁用时文字颜色，使用灰色，体现不可用状态 */
//    --ep-button-disabled-text-color: #9e9e9e;
//    /* 按钮禁用时背景颜色，使用较淡的灰色 */
//    --ep-button-disabled-bg-color: #e0e0e0;
//    /* 按钮禁用时边框颜色，使用更淡的灰色 */
//    --ep-button-disabled-border-color: #bdbdbd;
//   // --ep-button-text-color: var(--ep-color-primary);
//   // --ep-button-bg-color: var(--ep-color-primary-light-9);
//   // --ep-button-border-color: var(--ep-color-primary-light-5);
//   // --ep-button-hover-text-color: var(--ep-color-white);
//   // --ep-button-hover-bg-color: var(--ep-color-primary);
//   // --ep-button-hover-border-color: var(--ep-color-primary);
//   // --ep-button-active-text-color: var(--ep-color-white);
//   // /* 主色，多点灰度 */
//   // --ep-color-primary: #668cff;
//   // /* 白色 */
//   // --ep-color-white: #ffffff;
//   // /* 主色的较亮变种，用于按钮背景初始状态 */
//   // --ep-color-primary-light-9: #e8eaff;
//   // /* 主色的适中亮度变种，用于按钮边框初始状态 */
//   // --ep-color-primary-light-5: #aab3ff;

//   // /* 按钮文字颜色，默认使用主色 */
//   // --ep-button-text-color: var(--ep-color-primary);
//   // /* 按钮背景颜色，使用主色的较亮变种 */
//   // --ep-button-bg-color: var(--ep-color-primary-light-9);
//   // /* 按钮边框颜色，使用主色的适中亮度变种 */
//   // --ep-button-border-color: var(--ep-color-primary-light-5);
//   // /* 按钮悬停时文字颜色，使用白色 */
//   // --ep-button-hover-text-color: var(--ep-color-white);
//   // /* 按钮悬停时背景颜色，使用主色 */
//   // --ep-button-hover-bg-color: var(--ep-color-primary);
//   // /* 按钮悬停时边框颜色，使用主色 */
//   // --ep-button-hover-border-color: var(--ep-color-primary);
//   // /* 按钮激活时文字颜色，使用白色 */
//   // --ep-button-active-text-color: var(--ep-color-white);
//   // /* 按钮激活时背景颜色，使用比主色稍深一点的颜色，突出激活效果 */
//   // --ep-button-active-bg-color: #5273e6;
//   // /* 按钮激活时边框颜色，同样使用比主色稍深一点的颜色 */
//   // --ep-button-active-border-color: #5273e6;
//   // /* 按钮聚焦时文字颜色，使用白色 */
//   // --ep-button-focus-text-color: var(--ep-color-white);
//   // /* 按钮聚焦时背景颜色，使用主色 */
//   // --ep-button-focus-bg-color: var(--ep-color-primary);
//   // /* 按钮聚焦时边框颜色，使用主色 */
//   // --ep-button-focus-border-color: var(--ep-color-primary);
//   // /* 按钮禁用时文字颜色，使用灰色，体现不可用状态 */
//   // --ep-button-disabled-text-color: #9e9e9e;
//   // /* 按钮禁用时背景颜色，使用较淡的灰色 */
//   // --ep-button-disabled-bg-color: #e0e0e0;
//   // /* 按钮禁用时边框颜色，使用更淡的灰色 */
//   // --ep-button-disabled-border-color: #bdbdbd;
// }

body {
  font-family: var(--ep-font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
}

a {
  color: var(--ep-color-primary);
}

code {
  border-radius: var(--ep-border-radius-base);
  padding: var(--ep-spacing-mini) var(--ep-spacing-small);
  background-color: var(--ep-color-primary-light-9);
  color: var(--ep-color-primary);
}

button {
  color: var(--ep-button-text-color);
  background: var(--ep-button-bg-color);
  border: 1px solid var(--ep-button-border-color);
  padding: var(--ep-button-padding-vertical) var(--ep-button-padding-horizontal);
  border-radius: var(--ep-button-border-radius);
  cursor: pointer;
  transition: all var(--ep-transition-duration) var(--ep-transition-timing-function);
  font-weight: var(--ep-button-font-weight);

  &:hover {
    color: var(--ep-button-hover-text-color);
    background: var(--ep-button-hover-bg-color);
    border-color: var(--ep-button-hover-border-color);
  }

  &:active {
    color: var(--ep-button-active-text-color);
    background: var(--ep-button-active-bg-color);
    border-color: var(--ep-button-active-border-color);
  }

  &:focus {
    color: var(--ep-button-focus-text-color);
    background: var(--ep-button-focus-bg-color);
    border-color: var(--ep-button-focus-border-color);
  }

  &:disabled {
    color: var(--ep-button-disabled-text-color);
    background: var(--ep-button-disabled-bg-color);
    border-color: var(--ep-button-disabled-border-color);
    cursor: not-allowed;
  }
}

.ep-table thead th {
  font-size: var(--ep-font-size-base);
  font-weight: var(--ep-font-weight-primary);
  color: var(--ep-color-text-secondary);
}

/* Element Plus Header 组件样式覆盖 - 提高优先级 */
// .el-container .el-header.header {
//   padding: 0 30px !important;
// }

.ep-header {
  padding: 0 30px !important;
}

// /* 全局header类样式覆盖 */
// .header {
//   padding: 0 30px !important;
// }


