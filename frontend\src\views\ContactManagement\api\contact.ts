import request from '@/utils/request'
import type { CrmContacts } from '../types'

// 获取联系人列表
export const getContactsList = (params: any) => {
  return request({
    url: '/crm/contacts/list',
    method: 'get',
    params
  })
}

// 获取联系人详情
export const getContactsDetail = (id: string | number) => {
  return request({
    url: `/crm/contacts/${id}`,
    method: 'get'
  })
}

// 新增联系人
export const addContacts = (data: CrmContacts) => {
  return request({
    url: '/crm/contacts',
    method: 'post',
    data
  })
}

// 修改联系人
export const updateContacts = (data: CrmContacts) => {
  return request({
    url: `/crm/contacts/${data.id}`,
    method: 'put',
    data
  })
}

// 删除联系人
export const deleteContacts = (id: string | number) => {
  return request({
    url: `/crm/contacts/${id}`,
    method: 'delete'
  })
}

// 搜索客户
export const searchCustomers = (query: string) => {
  return request({
    url: '/crm/customer/search',
    method: 'get',
    params: { keyword: query }
  })
}

// 获取负责人列表
export const getResponsiblePersonList = () => {
  return request({
    url: '/system/user/list',
    method: 'get'
  })
} 