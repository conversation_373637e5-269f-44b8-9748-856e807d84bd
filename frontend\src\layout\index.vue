<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container" :class="{ 'collapse': isCollapse }">
      <div class="logo">
        <img src="@/assets/vue.svg" alt="Logo">
        <h1 v-show="!isCollapse">{{ title }}</h1>
      </div>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :collapse-transition="false"
        class="sidebar-menu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <template v-for="route in routes" :key="route.path">
          <!-- 没有子路由的菜单项 -->
          <el-menu-item v-if="!route.children" :index="route.path">
            <el-icon><component :is="route.meta?.icon" /></el-icon>
            <template #title>{{ route.meta?.title }}</template>
          </el-menu-item>
          
          <!-- 有子路由的菜单项 -->
          <el-sub-menu v-else :index="route.path">
            <template #title>
              <el-icon><component :is="route.meta?.icon" /></el-icon>
              <span>{{ route.meta?.title }}</span>
            </template>
            <el-menu-item 
              v-for="child in route.children"
              :key="child.path"
              :index="route.path + '/' + child.path"
            >
              <el-icon><component :is="child.meta?.icon" /></el-icon>
              <template #title>{{ child.meta?.title }}</template>
            </el-menu-item>
          </el-sub-menu>
        </template>
      </el-menu>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="navbar">
        <div class="left">
          <el-icon class="fold-btn" @click="toggleSidebar">
            <component :is="isCollapse ? 'Expand' : 'Fold'" />
          </el-icon>
          <breadcrumb />
        </div>
        <div class="right">
          <el-dropdown trigger="click">
            <span class="user-info">
              <img :src="userInfo.avatar" class="avatar">
              <span class="name">{{ userInfo.name }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleCommand('profile')">
                  <el-icon><User /></el-icon>个人中心
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleCommand('logout')">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 主要内容区 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user';
import { SwitchButton, User } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Breadcrumb from './components/Breadcrumb.vue';

const title = ref('CRM系统');
const isCollapse = ref(false);
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 用户信息
const userInfo = computed(() => ({
  name: userStore.name,
  avatar: userStore.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
}));

// 当前激活的菜单
const activeMenu = computed(() => route.path);

// 路由菜单
const routes = computed(() => {
  return router.options.routes.filter(route => {
    return route.path !== '/login' && route.path !== '/404' && route.path !== '/401';
  });
});

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value;
};

// 处理下拉菜单命令
const handleCommand = async (command: string) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      await userStore.logout();
      router.push('/login');
    } catch {
      // 取消操作
    }
  } else if (command === 'profile') {
    router.push('/user/profile');
  }
};
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
  
  &.collapse {
    width: 64px;
  }
  
  .logo {
    height: 50px;
    display: flex;
    align-items: center;
    padding: 10px;
    background: #2b2f3a;
    
    img {
      height: 32px;
      width: 32px;
      margin-right: 12px;
    }
    
    h1 {
      color: #fff;
      font-size: 16px;
      margin: 0;
      white-space: nowrap;
    }
  }
  
  .sidebar-menu {
    border: none;
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f0f2f5;
}

.navbar {
  height: 50px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  
  .left {
    display: flex;
    align-items: center;
    
    .fold-btn {
      padding: 0 15px;
      cursor: pointer;
      font-size: 16px;
      
      &:hover {
        background: rgba(0,0,0,.025);
      }
    }
  }
  
  .right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      .avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 8px;
      }
      
      .name {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.app-main {
  flex: 1;
  padding: 15px;
  overflow: auto;
}

// 路由切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
