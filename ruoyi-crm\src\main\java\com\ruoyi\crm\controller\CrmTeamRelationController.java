package com.ruoyi.crm.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.crm.service.ICrmTeamRelationService;

/**
 * 通用团队业务关联Controller
 * 
 * 根据深化团队管理实施方案-V2，提供分配和查询API
 * 支持联系人、线索、客户、商机等多种业务类型与团队的关联
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/crm/relation")
public class CrmTeamRelationController extends BaseController
{
    @Autowired
    private ICrmTeamRelationService crmTeamRelationService;

    /**
     * 查询通用团队业务关联列表
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmTeamRelation crmTeamRelation)
    {
        startPage();
        List<CrmTeamRelation> list = crmTeamRelationService.selectCrmTeamRelationList(crmTeamRelation);
        return getDataTable(list);
    }

    /**
     * 导出通用团队业务关联列表
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:export')")
    @Log(title = "通用团队业务关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmTeamRelation crmTeamRelation)
    {
        List<CrmTeamRelation> list = crmTeamRelationService.selectCrmTeamRelationList(crmTeamRelation);
        ExcelUtil<CrmTeamRelation> util = new ExcelUtil<CrmTeamRelation>(CrmTeamRelation.class);
        util.exportExcel(response, list, "通用团队业务关联数据");
    }

    /**
     * 获取通用团队业务关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(crmTeamRelationService.selectCrmTeamRelationById(id));
    }

    /**
     * 新增通用团队业务关联
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:add')")
    @Log(title = "通用团队业务关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmTeamRelation crmTeamRelation)
    {
        return toAjax(crmTeamRelationService.insertCrmTeamRelation(crmTeamRelation));
    }

    /**
     * 修改通用团队业务关联
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:edit')")
    @Log(title = "通用团队业务关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmTeamRelation crmTeamRelation)
    {
        return toAjax(crmTeamRelationService.updateCrmTeamRelation(crmTeamRelation));
    }

    /**
     * 删除通用团队业务关联
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:remove')")
    @Log(title = "通用团队业务关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(crmTeamRelationService.deleteCrmTeamRelationByIds(ids));
    }

    // ========== 核心业务API ==========

    /**
     * 分配业务对象到团队
     * 
     * @param teamId 团队ID
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:assign')")
    @Log(title = "分配团队", businessType = BusinessType.INSERT)
    @PostMapping("/assign")
    public AjaxResult assign(@RequestParam Long teamId, 
                           @RequestParam Long bizId, 
                           @RequestParam String bizType)
    {
        try {
            int result = crmTeamRelationService.assignTeamToBiz(teamId, bizId, bizType);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 取消业务对象的团队分配
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:unassign')")
    @Log(title = "取消团队分配", businessType = BusinessType.DELETE)
    @PostMapping("/unassign")
    public AjaxResult unassign(@RequestParam Long bizId, @RequestParam String bizType)
    {
        try {
            int result = crmTeamRelationService.unassignTeamFromBiz(bizId, bizType);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 根据业务对象查询所属团队
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 团队关联信息
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:query')")
    @GetMapping("/team")
    public AjaxResult getTeamByBiz(@RequestParam Long bizId, @RequestParam String bizType)
    {
        CrmTeamRelation relation = crmTeamRelationService.getTeamRelationByBiz(bizId, bizType);
        return success(relation);
    }

    /**
     * 根据团队ID查询关联的业务对象列表
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选）
     * @return 业务关联列表
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:query')")
    @GetMapping("/bizs")
    public AjaxResult getBizsByTeam(@RequestParam Long teamId, 
                                   @RequestParam(required = false) String bizType)
    {
        List<CrmTeamRelation> relations = crmTeamRelationService.getBizListByTeam(teamId, bizType);
        return success(relations);
    }

    /**
     * 批量分配业务对象到团队
     * 
     * @param teamId 团队ID
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:assign')")
    @Log(title = "批量分配团队", businessType = BusinessType.INSERT)
    @PostMapping("/batch-assign")
    public AjaxResult batchAssign(@RequestParam Long teamId, 
                                @RequestParam List<Long> bizIds, 
                                @RequestParam String bizType)
    {
        try {
            int result = crmTeamRelationService.batchAssignTeamToBiz(teamId, bizIds, bizType);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 批量取消业务对象的团队分配
     * 
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:unassign')")
    @Log(title = "批量取消团队分配", businessType = BusinessType.DELETE)
    @PostMapping("/batch-unassign")
    public AjaxResult batchUnassign(@RequestParam List<Long> bizIds, @RequestParam String bizType)
    {
        try {
            int result = crmTeamRelationService.batchUnassignTeamFromBiz(bizIds, bizType);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 检查业务对象是否已分配给团队
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 是否已分配
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:query')")
    @GetMapping("/check")
    public AjaxResult checkAssigned(@RequestParam Long bizId, @RequestParam String bizType)
    {
        boolean assigned = crmTeamRelationService.isBizAssigned(bizId, bizType);
        return success(assigned);
    }

    /**
     * 统计团队关联的业务对象数量
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选）
     * @return 关联数量
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:query')")
    @GetMapping("/count")
    public AjaxResult countByTeam(@RequestParam Long teamId, 
                                 @RequestParam(required = false) String bizType)
    {
        int count = crmTeamRelationService.countBizByTeam(teamId, bizType);
        return success(count);
    }

    /**
     * 根据业务类型统计各团队的业务对象数量
     * 
     * @param bizType 业务类型
     * @return 统计结果列表
     */
    @PreAuthorize("@ss.hasPermi('crm:relation:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam(required = false) String bizType)
    {
        List<CrmTeamRelation> statistics = crmTeamRelationService.countBizByTeamAndType(bizType);
        return success(statistics);
    }
}
