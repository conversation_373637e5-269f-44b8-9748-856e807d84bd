import request from '@/utils/request';
import { CrmTeam, CrmTeamMember } from '@/types/crm-team';

// 查询团队列表
export function listTeam(query?: any) {
  return request({
    url: '/crm/team/list',
    method: 'get',
    params: query
  });
}

// 查询团队详细信息
export function getTeam(teamId: number) {
  return request({
    url: `/crm/team/${teamId}`,
    method: 'get'
  });
}

// 新增团队
export function addTeam(data: CrmTeam) {
  return request({
    url: '/crm/team',
    method: 'post',
    data
  });
}

// 修改团队
export function updateTeam(data: CrmTeam) {
  return request({
    url: '/crm/team',
    method: 'put',
    data
  });
}

// 删除团队
export function deleteTeam(teamId: number) {
  return request({
    url: `/crm/team/${teamId}`,
    method: 'delete'
  });
}

// 获取团队成员列表
export function getTeamMembers(teamId: number) {
  return request({
    url: `/crm/team/members/${teamId}`,
    method: 'get'
  });
}

// 添加团队成员
export function addTeamMember(data: CrmTeamMember) {
  return request({
    url: '/crm/team/member',
    method: 'post',
    data
  });
}

// 批量添加团队成员
export function batchAddTeamMembers(teamId: number, userIds: number[], roleType: string) {
  return request({
    url: '/crm/team/member/batch',
    method: 'post',
    params: { teamId, userIds, roleType }
  });
}

// 移除团队成员
export function removeTeamMember(teamId: number, userId: number) {
  return request({
    url: `/crm/team/member/${teamId}/${userId}`,
    method: 'delete'
  });
}

// 获取可选用户列表
export function getAvailableUsers(deptId?: number) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: { deptId, status: '0' }
  });
}