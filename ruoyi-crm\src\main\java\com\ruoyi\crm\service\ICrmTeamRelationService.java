package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmTeamRelation;

/**
 * 通用团队业务关联Service接口
 * 
 * 根据深化团队管理实施方案-V2，实现通用的团队与业务模块关联
 * 支持联系人、线索、客户、商机等多种业务类型与团队的关联
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
public interface ICrmTeamRelationService 
{
    /**
     * 查询通用团队业务关联
     * 
     * @param id 通用团队业务关联主键
     * @return 通用团队业务关联
     */
    public CrmTeamRelation selectCrmTeamRelationById(Long id);

    /**
     * 查询通用团队业务关联列表
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 通用团队业务关联集合
     */
    public List<CrmTeamRelation> selectCrmTeamRelationList(CrmTeamRelation crmTeamRelation);

    /**
     * 新增通用团队业务关联
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 结果
     */
    public int insertCrmTeamRelation(CrmTeamRelation crmTeamRelation);

    /**
     * 修改通用团队业务关联
     * 
     * @param crmTeamRelation 通用团队业务关联
     * @return 结果
     */
    public int updateCrmTeamRelation(CrmTeamRelation crmTeamRelation);

    /**
     * 批量删除通用团队业务关联
     * 
     * @param ids 需要删除的通用团队业务关联主键集合
     * @return 结果
     */
    public int deleteCrmTeamRelationByIds(Long[] ids);

    /**
     * 删除通用团队业务关联信息
     * 
     * @param id 通用团队业务关联主键
     * @return 结果
     */
    public int deleteCrmTeamRelationById(Long id);

    // ========== 核心业务方法 ==========

    /**
     * 将业务对象分配给团队
     * 
     * @param teamId 团队ID
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 结果
     */
    public int assignTeamToBiz(Long teamId, Long bizId, String bizType);

    /**
     * 取消业务对象的团队分配
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 结果
     */
    public int unassignTeamFromBiz(Long bizId, String bizType);

    /**
     * 根据业务对象查询所属团队ID
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 团队ID，如果未分配则返回null
     */
    public Long getTeamIdByBiz(Long bizId, String bizType);

    /**
     * 根据业务对象查询团队关联信息
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 团队关联信息
     */
    public CrmTeamRelation getTeamRelationByBiz(Long bizId, String bizType);

    /**
     * 根据团队ID查询关联的业务对象列表
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选，为空则查询所有类型）
     * @return 业务关联列表
     */
    public List<CrmTeamRelation> getBizListByTeam(Long teamId, String bizType);

    /**
     * 批量分配业务对象到团队
     * 
     * @param teamId 团队ID
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @return 结果
     */
    public int batchAssignTeamToBiz(Long teamId, List<Long> bizIds, String bizType);

    /**
     * 批量取消业务对象的团队分配
     * 
     * @param bizIds 业务主键ID列表
     * @param bizType 业务类型
     * @return 结果
     */
    public int batchUnassignTeamFromBiz(List<Long> bizIds, String bizType);

    /**
     * 检查业务对象是否已分配给团队
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 是否已分配
     */
    public boolean isBizAssigned(Long bizId, String bizType);

    /**
     * 统计团队关联的业务对象数量
     * 
     * @param teamId 团队ID
     * @param bizType 业务类型（可选）
     * @return 关联数量
     */
    public int countBizByTeam(Long teamId, String bizType);

    /**
     * 根据业务类型统计各团队的业务对象数量
     * 
     * @param bizType 业务类型
     * @return 统计结果列表
     */
    public List<CrmTeamRelation> countBizByTeamAndType(String bizType);

    // ========== 业务验证方法 ==========

    /**
     * 验证团队是否存在
     * 
     * @param teamId 团队ID
     * @return 是否存在
     */
    public boolean validateTeamExists(Long teamId);

    /**
     * 验证业务对象是否存在
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 是否存在
     */
    public boolean validateBizExists(Long bizId, String bizType);

    /**
     * 验证业务类型是否支持
     * 
     * @param bizType 业务类型
     * @return 是否支持
     */
    public boolean validateBizType(String bizType);

    // ========== 业务名称获取方法 ==========

    /**
     * 根据业务对象获取业务名称
     * 
     * @param bizId 业务主键ID
     * @param bizType 业务类型
     * @return 业务名称
     */
    public String getBizName(Long bizId, String bizType);

    /**
     * 批量获取业务名称
     * 
     * @param relations 团队关联列表
     * @return 填充了业务名称的关联列表
     */
    public List<CrmTeamRelation> fillBizNames(List<CrmTeamRelation> relations);
}
