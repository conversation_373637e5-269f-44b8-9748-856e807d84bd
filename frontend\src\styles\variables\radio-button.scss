// Radio Button 样式变量
:root {
  // Radio Button 基础样式
  --ep-radio-button-font-weight: var(--ep-font-weight-primary);
  --ep-radio-button-border-radius: 4px;
  --ep-radio-button-padding-vertical: 8px;
  --ep-radio-button-padding-horizontal: 16px;
  --ep-radio-button-height: 32px;
  --ep-radio-button-font-size: 14px;

  // 默认状态
  --ep-radio-button-text-color: var(--ep-text-color-regular);
  --ep-radio-button-bg-color: #fff;
  --ep-radio-button-border-color: var(--ep-border-color);

  // 悬停状态
  --ep-radio-button-hover-text-color: #fff;
  --ep-radio-button-hover-bg-color: linear-gradient(90deg, #1a75ff 0%, #668cff 100%);
  --ep-radio-button-hover-border-color: transparent;

  // 激活状态（is-active）
  --ep-radio-button-active-text-color: #fff;
  --ep-radio-button-active-bg-color: linear-gradient(90deg, #0066ff 0%, #4d79ff 100%);
  --ep-radio-button-active-border-color: transparent;

  // 禁用状态
  --ep-radio-button-disabled-text-color: var(--ep-disabled-text-color);
  --ep-radio-button-disabled-bg-color: var(--ep-disabled-bg-color);
  --ep-radio-button-disabled-border-color: var(--ep-disabled-border-color);
} 