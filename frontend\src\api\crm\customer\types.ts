// 客户实体接口
export interface Customer {
  id: number
  name: string
  contactPerson: string
  phone: string
  email: string
  address: string
  industry?: string
  size?: string
  status: 'active' | 'inactive' | 'pending'
  source?: string
  description?: string
  createdAt: string
  updatedAt: string
}

// 查询参数接口
export interface CustomerQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: string
  industry?: string
  source?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 创建客户DTO
export interface CreateCustomerDTO {
  name: string
  contactPerson: string
  phone: string
  email: string
  address: string
  industry?: string
  size?: string
  status?: 'active' | 'inactive' | 'pending'
  source?: string
  description?: string
}

// 更新客户DTO
export type UpdateCustomerDTO = Partial<CreateCustomerDTO>

// 客户统计信息接口
export interface CustomerStatistics {
  totalCount: number
  activeCount: number
  inactiveCount: number
  pendingCount: number
  monthlyGrowth: number
  industryDistribution: Record<string, number>
  sourceDistribution: Record<string, number>
}

// 客户搜索参数
export interface CustomerSearchParams {
    keyword?: string;
    recent?: boolean;
    limit?: number;
    pageNum?: number;
    pageSize?: number;
}

// 客户数据
export interface CustomerData {
    id: number;
    customerName: string;
    industry: string;
    createdTime: string;
    updatedTime: string;
    createdBy: number;
    updatedBy: number;
}
