package com.ruoyi.crm.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.domain.entity.CrmTeamMember;
import com.ruoyi.crm.service.ICrmTeamMemberService;

/**
 * 团队成员Controller
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/crm/team-member")
public class CrmTeamMemberController extends BaseController
{
    @Autowired
    private ICrmTeamMemberService crmTeamMemberService;

    /**
     * 查询团队成员列表
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmTeamMember crmTeamMember)
    {
        startPage();
        List<CrmTeamMember> list = crmTeamMemberService.selectCrmTeamMemberList(crmTeamMember);
        return getDataTable(list);
    }

    /**
     * 导出团队成员列表
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:export')")
    @Log(title = "团队成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmTeamMember crmTeamMember)
    {
        List<CrmTeamMember> list = crmTeamMemberService.selectCrmTeamMemberList(crmTeamMember);
        ExcelUtil<CrmTeamMember> util = new ExcelUtil<CrmTeamMember>(CrmTeamMember.class);
        util.exportExcel(response, list, "团队成员数据");
    }

    /**
     * 获取团队成员详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(crmTeamMemberService.selectCrmTeamMemberById(id));
    }

    /**
     * 新增团队成员
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:add')")
    @Log(title = "团队成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmTeamMember crmTeamMember)
    {
        return toAjax(crmTeamMemberService.insertCrmTeamMember(crmTeamMember));
    }

    /**
     * 修改团队成员
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:edit')")
    @Log(title = "团队成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmTeamMember crmTeamMember)
    {
        return toAjax(crmTeamMemberService.updateCrmTeamMember(crmTeamMember));
    }

    /**
     * 删除团队成员
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:remove')")
    @Log(title = "团队成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(crmTeamMemberService.deleteCrmTeamMemberByIds(ids));
    }

    // ========== 业务API ==========

    /**
     * 根据团队ID查询成员列表
     * 
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:query')")
    @GetMapping("/team/{teamId}")
    public AjaxResult getTeamMembers(@PathVariable("teamId") Long teamId)
    {
        List<CrmTeamMember> members = crmTeamMemberService.getTeamMembersByTeamId(teamId);
        return success(members);
    }

    /**
     * 根据用户ID查询所属团队列表
     * 
     * @param userId 用户ID
     * @return 团队列表
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:query')")
    @GetMapping("/user/{userId}")
    public AjaxResult getUserTeams(@PathVariable("userId") Long userId)
    {
        List<CrmTeamMember> teams = crmTeamMemberService.getTeamsByUserId(userId);
        return success(teams);
    }

    /**
     * 添加团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param roleType 角色类型
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:add')")
    @Log(title = "添加团队成员", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addMember(@RequestParam Long teamId, 
                               @RequestParam Long userId, 
                               @RequestParam(required = false, defaultValue = "member") String roleType)
    {
        try {
            int result = crmTeamMemberService.addTeamMember(teamId, userId, roleType);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 批量添加团队成员
     * 
     * @param teamId 团队ID
     * @param userIds 用户ID列表
     * @param roleType 角色类型
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:add')")
    @Log(title = "批量添加团队成员", businessType = BusinessType.INSERT)
    @PostMapping("/batch-add")
    public AjaxResult batchAddMembers(@RequestParam Long teamId, 
                                     @RequestParam List<Long> userIds, 
                                     @RequestParam(required = false, defaultValue = "member") String roleType)
    {
        try {
            int result = crmTeamMemberService.batchAddTeamMembers(teamId, userIds, roleType);
            return success("成功添加 " + result + " 个团队成员");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 更新成员角色
     * 
     * @param id 团队成员ID
     * @param roleType 新角色类型
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:edit')")
    @Log(title = "更新团队成员角色", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/role")
    public AjaxResult updateRole(@PathVariable("id") Long id, @RequestParam String roleType)
    {
        return toAjax(crmTeamMemberService.updateMemberRole(id, roleType));
    }

    /**
     * 移除团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:remove')")
    @Log(title = "移除团队成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public AjaxResult removeMember(@RequestParam Long teamId, @RequestParam Long userId)
    {
        return toAjax(crmTeamMemberService.removeTeamMember(teamId, userId));
    }

    /**
     * 批量移除团队成员
     * 
     * @param userIds 用户ID列表
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:remove')")
    @Log(title = "批量移除团队成员", businessType = BusinessType.DELETE)
    @PostMapping("/batch-remove")
    public AjaxResult batchRemoveMembers(@RequestBody List<Long> userIds)
    {
        try {
            int result = crmTeamMemberService.batchRemoveTeamMembers(userIds);
            return success("成功移除 " + result + " 个团队成员");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 根据业务对象获取团队成员
     * 
     * @param bizId 业务对象ID
     * @param bizType 业务类型
     * @return 团队成员列表
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:query')")
    @GetMapping("/biz")
    public AjaxResult getTeamMembersByBiz(@RequestParam Long bizId, @RequestParam String bizType)
    {
        List<CrmTeamMember> members = crmTeamMemberService.getTeamMembersByBiz(bizId, bizType);
        return success(members);
    }

    /**
     * 检查用户是否为团队成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否为团队成员
     */
    @PreAuthorize("@ss.hasPermi('crm:team-member:query')")
    @GetMapping("/check")
    public AjaxResult checkMembership(@RequestParam Long teamId, @RequestParam Long userId)
    {
        boolean isMember = crmTeamMemberService.isTeamMember(teamId, userId);
        String role = crmTeamMemberService.getUserRoleInTeam(teamId, userId);
        
        return success()
            .put("isMember", isMember)
            .put("role", role);
    }
}
