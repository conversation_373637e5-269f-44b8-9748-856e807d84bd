import request from '@/utils/request'

// 查询客户列表
export function listCustomers(query) {
  return request({
    url: '/crm/customers/list',
    method: 'get',
    params: query
  })
}

// 查询客户详细
export function getCustomers(id) {
  return request({
    url: '/crm/customers/' + id,
    method: 'get'
  })
}

// 新增客户
export function addCustomers(data) {
  return request({
    url: '/crm/customers',
    method: 'post',
    data: data
  })
}

// 修改客户
export function updateCustomers(data) {
  return request({
    url: '/crm/customers',
    method: 'put',
    data: data
  })
}

// 删除客户
export function delCustomers(id) {
  return request({
    url: '/crm/customers/' + id,
    method: 'delete'
  })
}
